package com.wosai.upay.job.xxljob.batch;

import com.wosai.data.util.CollectionUtil;
import com.wosai.upay.job.biz.BusinessRuleBiz;
import com.wosai.upay.job.biz.WeixinCustomGoldBiz;
import com.wosai.upay.job.xxljob.model.BatchJobParam;
import com.wosai.upay.job.model.CommonModel;
import com.wosai.upay.job.model.DO.MerchantProviderParams;
import com.wosai.upay.job.util.ChatBotUtil;
import com.wosai.upay.job.util.DefaultValueUtil;
import com.wosai.upay.job.util.ExceptionUtil;
import com.wosai.upay.job.xxljob.template.AbstractBatchJobHandler;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.BeanPropertyRowMapper;
import org.springframework.jdbc.core.namedparam.NamedParameterJdbcTemplate;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * @Description:微信点金计划商家小票管理
 * @see: https://pay.weixin.qq.com/wiki/doc/apiv3/wxpay/goldplan/chapter1_1.shtml
 * <AUTHOR>
 * Date 2020/7/15 9:47 上午
 **/
@Component("WeixinCustomGoldJobHandler")
@Slf4j
public class WeixinCustomGoldJobHandler extends AbstractBatchJobHandler<MerchantProviderParams> {

    private static final String KEY_OPEN_CUSTOM_GOLD = "open_custom_gold";
    private static final long TEN_MINUTES = 10;
    private static final int LIMIT_SIZE = 8;
    @Autowired
    ChatBotUtil chatBotUtil;
    @Resource(name = "merchantContractNPJdbcTemplate")
    NamedParameterJdbcTemplate merchantContractNPJdbcTemplate;
    @Autowired
    WeixinCustomGoldBiz weixinCustomGoldBiz;
    @Autowired
    private BusinessRuleBiz businessRuleBiz;

    @Override
    public List<MerchantProviderParams> queryTaskItems(BatchJobParam param) {
        if (!businessRuleBiz.shouldOpenGold()) {
            log.info("点金计划配置路由不开通");
            return new ArrayList<>();
        }
        long start = getStartTime(Optional.of(param.getQueryTime()).orElse(TEN_MINUTES));
        Integer limit = Optional.of(param.getBatchSize()).orElse(LIMIT_SIZE);
        List<MerchantProviderParams> params = listWeixinParams(start, limit);
        params = weixinCustomGoldBiz.filtParams(params);
        if (CollectionUtils.isEmpty(params)) {
            return new ArrayList<>();
        }
        log.info("需要开通点金计划商户数 {}", params.size());
        return params;
    }

    @Override
    public String getLockKey(MerchantProviderParams merchantProviderParams) {
        return KEY_OPEN_CUSTOM_GOLD + merchantProviderParams.getId();
    }

    @Override
    public void doHandleSingleData(MerchantProviderParams param) {
        try {
            if (weixinCustomGoldBiz.extParamExist(param)) {
                return;
            }
            weixinCustomGoldBiz.open(param);
        } catch (Exception e) {
            log.error("{} 开通点金计划错误", param.getMerchant_sn(), e);
            chatBotUtil.sendMessageToContractWarnChatBot("开通点金计划错误" + ExceptionUtil.getThrowableMsg(e) + "商户号:" + param.getMerchant_sn());
        }
    }

    private long getStartTime(long queryTime) {
        LocalDateTime now = LocalDateTime.now();
        now = now.minusMinutes(DefaultValueUtil.value(queryTime, TEN_MINUTES));
        return now.toInstant(ZoneOffset.of("+8")).toEpochMilli();
    }


    /**
     * 需要扫描的params列表
     */
    public List<MerchantProviderParams> listWeixinParams(long startTime, int limit) {
        String sql = "select * from merchant_provider_params where payway =:payway and mtime >:mtime " +
                "and provider!=:payway and params_config_status=1 and gold_status=0 and status=1 and channel_no not in ('178700778','217996102','176174614','**********')" +
                " order by mtime desc limit :limit";
        Map params = CollectionUtil.hashMap(CommonModel.PAYWAY, 3,
                CommonModel.MTIME, startTime,
                "limit", limit
        );
        return merchantContractNPJdbcTemplate.query(sql, params, new BeanPropertyRowMapper<>(MerchantProviderParams.class));
    }
}
