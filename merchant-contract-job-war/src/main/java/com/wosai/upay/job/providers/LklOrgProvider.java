package com.wosai.upay.job.providers;

import avro.shaded.com.google.common.collect.Lists;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.shouqianba.cua.enums.contract.ContractSubTaskProcessStatusEnum;
import com.shouqianba.cua.enums.contract.ContractSubTaskTypeEnum;
import com.shouqianba.cua.enums.contract.ProviderEnum;
import com.shouqianba.cua.enums.core.PaywayEnum;
import com.shouqianba.cua.utils.object.ConvertUtil;
import com.wosai.common.utils.WosaiMapUtils;
import com.wosai.data.bean.BeanUtil;
import com.wosai.data.dao.DaoConstants;
import com.wosai.data.util.CollectionUtil;
import com.wosai.trade.service.result.ListMchFeeRateResult;
import com.wosai.upay.core.model.MerchantConfig;
import com.wosai.upay.core.model.TransactionParam;
import com.wosai.upay.job.enume.ScheduleEnum;
import com.wosai.upay.job.exception.ContextParamException;
import com.wosai.upay.job.model.*;
import com.wosai.upay.job.model.DO.MerchantProviderParams;
import com.wosai.upay.job.refactor.dao.ContractSubTaskExtDAO;
import com.wosai.upay.job.refactor.event.PayMchUpdateSuccessEvent;
import com.wosai.upay.job.refactor.model.entity.ContractSubTaskDO;
import com.wosai.upay.job.refactor.model.entity.ContractSubTaskExtDO;
import com.wosai.upay.job.util.ProviderUtil;
import com.wosai.upay.job.util.StringUtil;
import com.wosai.upay.merchant.contract.constant.LakalaConstant;
import com.wosai.upay.merchant.contract.exception.ContractBizException;
import com.wosai.upay.merchant.contract.model.ContractResponse;
import com.wosai.upay.merchant.contract.model.WeixinConfig;
import com.wosai.upay.merchant.contract.model.provider.UnionAlipayParam;
import com.wosai.upay.merchant.contract.model.provider.UnionWeixinParam;
import com.wosai.upay.merchant.contract.model.terminal.*;
import com.wosai.upay.merchant.contract.service.NewUnionService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.MapUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2022/5/7
 */
@Slf4j
@Component(ProviderUtil.LKL_ORG_PROVIDER_CHANNEL)
public class LklOrgProvider extends AbstractProvider {

    @Autowired
    private NewUnionService newUnionService;

    @Autowired
    private ContractSubTaskExtDAO contractSubTaskExtDAO;

    @Autowired
    private ApplicationEventPublisher applicationEventPublisher;

    @Override
    protected ContractSubTask produceUpdateTaskByRule(String merchantSn, ContractEvent event, Map<String, Object> paramContext, ContractRule contractRule) {
        ContractSubTask subTask = new ContractSubTask()
                .setMerchant_sn(merchantSn)
                .setStatus_influ_p_task(contractRule.getUpdateInfluPtask())
                .setChannel(ProviderUtil.LKL_ORG_PROVIDER_CHANNEL)
                .setChange_config(0)
                .setDefault_channel(contractRule.getDefault())
                .setPayway(contractRule.getPayway())
                .setContract_id(contractRule.getChannelNo())
                .setContract_rule(contractRule.getRule())
                .setRule_group_id(event.getRule_group_id())
                .setTask_type(ProviderUtil.SUB_TASK_TASK_TYPE_UPDATE)
                .setSchedule_status(ScheduleEnum.SCHEDULE_ENABLE.getValue())
                .setRetry(0);


        Map eventMsg = JSON.parseObject(event.getEvent_msg(), Map.class);
        if (org.springframework.util.CollectionUtils.isEmpty(eventMsg)) {
            log.error("lkl_org channelNo {} 更新事件 {} eventMsg为空", contractRule.getChannelNo(), event.getId());
            return null;
        }
        if (WosaiMapUtils.getBooleanValue(eventMsg, ContractEvent.FORCE_UPDATE)) {
            subTask.setTask_type(ProviderUtil.SUB_TASK_TASK_TYPE_UPDATE);
            return subTask;
        }
        List<String> changeFileds = (List) eventMsg.get("msg");
        if (!ProviderUtil.SUB_TASK_TASK_TYPE_CRM_BOTH.equals(event.getEvent_type()) && CollectionUtils.isEmpty(changeFileds)) {
            log.error("lkl_org channelNo {} 更新事件 {} msg为空", contractRule.getChannelNo(), event.getId());
            return null;
        }
        if (ProviderUtil.getPayWayUpdate(contractRule.getPayway(), event.getEvent_type(), changeFileds)) {
            subTask.setTask_type(ProviderUtil.SUB_TASK_TASK_TYPE_UPDATE);
            return subTask;
        }

        return null;
    }

    @Override
    protected ContractResponse processInsertTaskByRule(ContractTask contractTask, ContractChannel contractChannel, ContractSubTask sub) {
        Integer payWay = sub.getPayway();
        Map contextParam = JSON.parseObject(contractTask.getEvent_context(), Map.class);
        //银联接口
        if (PaywayEnum.ALIPAY.getValue().equals(payWay)) {
            UnionAlipayParam alipayParam = buildParam(contractChannel, sub, UnionAlipayParam.class);
            return newUnionService.contractAlipayWithParams(contextParam, alipayParam);
        }
        if (PaywayEnum.WEIXIN.getValue().equals(payWay)) {
            UnionWeixinParam weixinParam = buildParam(contractChannel, sub, UnionWeixinParam.class);
            return newUnionService.contractWeixinWithParams(contextParam, weixinParam);
        }

        return null;
    }

    @Override
    protected ContractResponse processUpdateTaskByRule(ContractTask contractTask, ContractChannel contractChannel, ContractSubTask sub) {
        Map contextParam = JSON.parseObject(contractTask.getEvent_context(), Map.class);
        Integer payWay = sub.getPayway();

        Optional<ContractSubTaskExtDO> subTaskExtOP = contractSubTaskExtDAO.getBySubTaskId(sub.getId());

        if (PaywayEnum.ALIPAY.getValue().equals(payWay)) {
            UnionAlipayParam alipayParam = buildParam(contractChannel, sub, UnionAlipayParam.class);
            ContractResponse response = subTaskExtOP.isPresent() ? newUnionService.updateAlipayBySubMchId(subTaskExtOP.get().getPayMerchantId(), contextParam, alipayParam) :
                    newUnionService.updateAlipayWithParams(contextParam, alipayParam);
            if (response.isSuccess() && !subTaskExtOP.isPresent()) {
                applicationEventPublisher.publishEvent(new PayMchUpdateSuccessEvent(sub,
                        contractTask.getMerchant_sn(),
                        ProviderEnum.PROVIDER_LKLORG.getValue(),
                        PaywayEnum.ALIPAY.getValue()));
            }
            return response;
        }
        if (PaywayEnum.WEIXIN.getValue().equals(payWay)) {
            UnionWeixinParam weixinParam = buildParam(contractChannel, sub, UnionWeixinParam.class);
            ContractResponse response = subTaskExtOP.isPresent() ? newUnionService.updateWechatBySubMchId(subTaskExtOP.get().getPayMerchantId(), contextParam, weixinParam) :
                    newUnionService.updateWechatWithParams(contextParam, weixinParam);
            if (response.isSuccess() && !subTaskExtOP.isPresent()) {
                applicationEventPublisher.publishEvent(new PayMchUpdateSuccessEvent(sub,
                        contractTask.getMerchant_sn(),
                        ProviderEnum.PROVIDER_LKLORG.getValue(),
                        PaywayEnum.WEIXIN.getValue()));
            }
            return response;
        }

        return null;
    }

    @Override
    public Map doWeixinSubDevConfig(WeixinConfig weixinConfig, MerchantProviderParams param) {
        UnionWeixinParam unionWeixinParam = contractParamsBiz.buildContractParamsByParams(param, UnionWeixinParam.class);
        return unionService.weixinSubdevConfigV2(unionWeixinParam, weixinConfig);
    }

    /**
     * 终端/子商户号绑定
     *
     * @param termInfoDTO
     * @param payWay
     * @param terminalSn
     * @return
     */
    @Override
    public ContractResponse boundTerminal(AddTermInfoDTO termInfoDTO, int payWay, String terminalSn) {
        ContractResponse response = null;
        if (payWay == PaywayEnum.ALIPAY.getValue()) {
            UnionAlipayParam alipayParam = contractParamsBiz.buildContractParamsByPayMchId(termInfoDTO.getSubMchId(), UnionAlipayParam.class);
            response = newUnionService.addAliTermInfo(termInfoDTO, alipayParam);
        }
        if (payWay == PaywayEnum.WEIXIN.getValue()) {
            UnionWeixinParam weixinParam = contractParamsBiz.buildContractParamsByPayMchId(termInfoDTO.getSubMchId(), UnionWeixinParam.class);
            response = newUnionService.addWxTermInfo(termInfoDTO, weixinParam);
        }
        return response;
    }


    @Override
    public ContractResponse unbindTerminal(LogOutTermInfoDTO dto, int payWay, String terminalSn) {
        ContractResponse response = null;
        if (payWay == PaywayEnum.ALIPAY.getValue()) {
            UnionAlipayParam alipayParam = contractParamsBiz.buildContractParamsByPayMchId(dto.getSubMchId(), UnionAlipayParam.class);
            response = newUnionService.LogOutAliTermInfo(dto, alipayParam);
        }
        if (payWay == PaywayEnum.WEIXIN.getValue()) {
            UnionWeixinParam weixinParam = contractParamsBiz.buildContractParamsByPayMchId(dto.getSubMchId(), UnionWeixinParam.class);
            response = newUnionService.LogOutWxTermInfo(dto, weixinParam);
        }
        return response;
    }

    @Override
    public ContractResponse updateTerminal(UpdateTermInfoDTO dto, int payWay, String terminalSn) {
        ContractResponse response = null;
        if (payWay == PaywayEnum.ALIPAY.getValue()) {
            UnionAlipayParam alipayParam = contractParamsBiz.buildContractParamsByPayMchId(dto.getSubMchId(), UnionAlipayParam.class);
            response = newUnionService.updateAliTermInfo(dto, alipayParam);
        }
        if (payWay == PaywayEnum.WEIXIN.getValue()) {
            UnionWeixinParam weixinParam = contractParamsBiz.buildContractParamsByPayMchId(dto.getSubMchId(), UnionWeixinParam.class);
            response = newUnionService.updateWxTermInfo(dto, weixinParam);
        }
        return response;
    }


    @Override
    public ContractResponse updateTerminalWithCustom(UpdateTermInfoDTO dto,
                                                     int payWay,
                                                     String terminalSn,
                                                     AliTermInfoRequest customAliTermInfo,
                                                     WxTermInfoRequest wxCustomInfo) {
        ContractResponse response = null;
        if (payWay == PaywayEnum.ALIPAY.getValue()) {
            UnionAlipayParam alipayParam = contractParamsBiz.buildContractParamsByPayMchId(dto.getSubMchId(), UnionAlipayParam.class);
            response = newUnionService.updateAliTermInfoWihCustom(dto, alipayParam, customAliTermInfo);
        }
        if (payWay == PaywayEnum.WEIXIN.getValue()) {
            UnionWeixinParam weixinParam = contractParamsBiz.buildContractParamsByPayMchId(dto.getSubMchId(), UnionWeixinParam.class);
            response = newUnionService.updateWxTermInfoWithCustom(dto, weixinParam, wxCustomInfo);
        }
        return response;
    }

    @Override
    public List getFeeRate(String merchantId) {
        if (StringUtil.empty(merchantId)) {
            return null;
        }
        List<Map> merchantConfigs = tradeConfigService.getAnalyzedMerchantConfigs(merchantId);
        Map weixinConfig = null;
        Map alipayConfig = null;
        Map unionConfig = null;
        Map bestPayConfig = null;
        for (Map merchantConfig : merchantConfigs) {
            int payway = BeanUtil.getPropInt(merchantConfig, MerchantConfig.PAYWAY);
            if (payway == PaywayEnum.ALIPAY.getValue()) {
                alipayConfig = merchantConfig;
            } else if (payway == PaywayEnum.WEIXIN.getValue()) {
                weixinConfig = merchantConfig;
            } else if (payway == PaywayEnum.UNIONPAY.getValue()) { //银联二维码支付
                unionConfig = merchantConfig;
            } else if (payway == PaywayEnum.BESTPAY.getValue()) {
                bestPayConfig = merchantConfig;
            }
        }
        if (weixinConfig == null || alipayConfig == null) {
            throw new ContextParamException("商户的支付宝或者微信交易配置merchant_config未配置");
        }
        String alipayFeerate = getLadderFee(alipayConfig, MerchantConfig.WAP_FEE_RATE);
        //String weixinFeerate = getLadderFee(weixinConfig, MerchantConfig.WAP_FEE_RATE);
        FeeData weixinFeerate = getLadderFeeV2(weixinConfig, MerchantConfig.WAP_FEE_RATE);
        String unionFeeRate = getLadderFee(unionConfig, MerchantConfig.WAP_FEE_RATE);
        String bestPayFeeRate = getLadderFee(bestPayConfig, MerchantConfig.B2C_FEE_RATE);
        List list = Lists.newArrayList(
                //CollectionUtil.hashMap(LakalaConstant.FEERATE_TYPECODE, LakalaConstant.FEERATE_CODE_WECHAT, LakalaConstant.FEERATE_PCT, weixinFeerate),
                CollectionUtil.hashMap(LakalaConstant.FEERATE_TYPECODE, LakalaConstant.FEERATE_CODE_ALIPAY, LakalaConstant.FEERATE_PCT, alipayFeerate),
                CollectionUtil.hashMap(LakalaConstant.FEERATE_TYPECODE, LakalaConstant.FEERATE_CODE_BESTPAY, LakalaConstant.FEERATE_PCT, bestPayFeeRate)
        );
        //添加微信费率
        handleFee(list, weixinFeerate, TYPE_WEIXIN);
        //云闪付非分级费率
        if (!isLadderFeeRate(unionConfig)) {
            list.add(CollectionUtil.hashMap(LakalaConstant.FEERATE_TYPECODE, "314", LakalaConstant.FEERATE_PCT, unionFeeRate));
            list.add(CollectionUtil.hashMap(LakalaConstant.FEERATE_TYPECODE, "315", LakalaConstant.FEERATE_PCT, unionFeeRate));
            return list;
        }
        //设置lklV3云闪付阶梯费率,详情参见https://jira.wosai-inc.com/browse/CUA-2939
        final List<ListMchFeeRateResult.LadderFeeRate> ladderFeeRates = JSONObject.parseArray(JSONObject.toJSONString(BeanUtil.getProperty(unionConfig, MerchantConfig.LADDER_FEE_RATES)), ListMchFeeRateResult.LadderFeeRate.class);
        //按照从小到大排序
        ladderFeeRates.sort(Comparator.comparing(ListMchFeeRateResult.LadderFeeRate::getMax, Comparator.nullsLast(Double::compareTo)));
        //以阶梯费率的max属性为key,wap_fee_rate值为value;
        final Map<Double, String> map = ladderFeeRates.stream().collect(Collectors.toMap(entry -> entry.getMax(), entry -> entry.getWapFeeRate(), (k1, k2) -> k1));
        //阶梯费率是否在1000以下
        final boolean match = map.keySet().stream().filter(Objects::nonNull).allMatch(key -> key.compareTo(1000.0) < 0);
        //分级1000以下
        if (match) {
            list.add(CollectionUtil.hashMap(LakalaConstant.FEERATE_TYPECODE, "314", LakalaConstant.FEERATE_PCT, unionFeeRate));
            list.add(CollectionUtil.hashMap(LakalaConstant.FEERATE_TYPECODE, "315", LakalaConstant.FEERATE_PCT, unionFeeRate));
            return list;
        }
        //分级1000以上
        final List<ListMchFeeRateResult.LadderFeeRate> rates = ladderFeeRates.stream().filter(x -> rangeInDefined(1000.0, x.getMin(), x.getMax())).limit(1).collect(Collectors.toList());
        //包含1000块的区间
        final ListMchFeeRateResult.LadderFeeRate rangeInDefinedRate = rates.get(0);
        //1000以下的最大费率
        list.add(CollectionUtil.hashMap(LakalaConstant.FEERATE_TYPECODE, "413", LakalaConstant.FEERATE_PCT, rangeInDefinedRate.getWapFeeRate()));
        list.add(CollectionUtil.hashMap(LakalaConstant.FEERATE_TYPECODE, "414", LakalaConstant.FEERATE_PCT, rangeInDefinedRate.getWapFeeRate()));
        list.add(CollectionUtil.hashMap(LakalaConstant.FEERATE_TYPECODE, "314", LakalaConstant.FEERATE_PCT, unionFeeRate));
        list.add(CollectionUtil.hashMap(LakalaConstant.FEERATE_TYPECODE, "315", LakalaConstant.FEERATE_PCT, unionFeeRate));
        return list;
    }


    @Override
    public String getMerchantTermNo(String merchantSn) {
        final Map merchant = merchantService.getMerchantBySn(merchantSn);
        final String merchantId = BeanUtil.getPropString(merchant, DaoConstants.ID);
        Map merchantConfig = this.tradeConfigService.getMerchantConfigByMerchantIdAndPayway(merchantId, null);
        final Object paramObject = Optional.ofNullable(merchantConfig).map(config -> config.get(MerchantConfig.PARAMS)).orElseThrow(() -> new ContractBizException("不存在该商户或者params不存在"));
        Map params = JSON.parseObject(JSON.toJSONString(paramObject), Map.class);
        String lakalaTerminalId = null;
        if (params != null) {
            Map lakalaTradeParams = JSON.parseObject(MapUtils.getString(params, TransactionParam.LAKALA_TRADE_PARAMS));
            lakalaTerminalId = MapUtils.getString(lakalaTradeParams, TransactionParam.LAKALA_TERM_ID);
        }
        return lakalaTerminalId;
    }


    @Override
    public ContractResponse doProcessMicroUpgradeTaskByRule(ContractTask contractTask, ContractChannel contractChannel, ContractSubTask sub) {
        Integer payWay = sub.getPayway();
        Map contextParam = JSON.parseObject(contractTask.getEvent_context(), Map.class);
        final Map<String, String> lklorgInfoByResponse = getLklorgInfoByResponse(contractTask.getId());
        //银联接口
        if (PaywayEnum.ALIPAY.getValue().equals(payWay)) {
            UnionAlipayParam alipayParam = buildParam(contractChannel, sub, UnionAlipayParam.class);
            return newUnionService.microUpgradeContractAlipayWithParams(contextParam, alipayParam,lklorgInfoByResponse.get("merCupNo"));
        }
        if (PaywayEnum.WEIXIN.getValue().equals(payWay)) {
            UnionWeixinParam weixinParam = buildParam(contractChannel, sub, UnionWeixinParam.class);
            return newUnionService.microUpgradeContractWeixinWithParams(contextParam,weixinParam,lklorgInfoByResponse.get("merCupNo"));
        }

        return null;
    }



    /**
     * 通过入网任务返回获取拉卡拉商户号和银联商户号
     * @param pTaskId
     * @return
     */
    public Map<String, String> getLklorgInfoByResponse(Long pTaskId) {
        final List<ContractSubTaskDO> subTaskDOS = contractSubTaskDAO.listByPTaskId(pTaskId);
        final Optional<ContractSubTaskDO> first = subTaskDOS.stream().filter(r -> PaywayEnum.ACQUIRER.getValue().equals(r.getPayway())
                && Objects.equals(r.getStatus(), ContractSubTaskProcessStatusEnum.PROCESS_SUCCESS.getValue())
                && Objects.equals(r.getScheduleDepTaskId(), 0L)
                && Objects.equals(r.getTaskType(), ContractSubTaskTypeEnum.MERCHANT_OPENING.getValue())
        ).findFirst();
        if(!first.isPresent()) {
           throw new ContractBizException("没有找到对应的入网任务");
        }
        final String responseBody = first.get().getResponseBody();
        Optional<Map> callbackMsgOpt = ConvertUtil.castToExpectedList(BeanUtil.getNestedProperty(responseBody, "callback_msg"), Map.class).stream().findFirst();
        if (!callbackMsgOpt.isPresent()) {
            throw new ContractBizException("拉卡拉机构进件子任务返回结果callback_msg为空, subTask id = " + first.get().getId());
        }
        //银联商户号
        final String merCupNo = BeanUtil.getNestedProperty(callbackMsgOpt.get(), "data.merCupNo").toString();
        //拉卡拉商户号
        final String merInnerNo = BeanUtil.getNestedProperty(callbackMsgOpt.get(), "data.merInnerNo").toString();
        return CollectionUtil.hashMap("merCupNo", merCupNo, "merInnerNo", merInnerNo);

    }
}