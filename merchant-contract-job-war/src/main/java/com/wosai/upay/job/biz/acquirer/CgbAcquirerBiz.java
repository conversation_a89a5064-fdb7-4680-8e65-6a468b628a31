package com.wosai.upay.job.biz.acquirer;

import com.shouqianba.cua.enums.contract.AcquirerTypeEnum;
import com.shouqianba.cua.enums.contract.ProviderEnum;
import com.shouqianba.cua.enums.core.PaywayEnum;
import com.wosai.data.bean.BeanUtil;
import com.wosai.upay.job.Constants.McConstant;
import com.wosai.upay.job.biz.AgentAppidBiz;
import com.wosai.upay.job.biz.ContractParamsBiz;
import com.wosai.upay.job.biz.RuleContext;
import com.wosai.upay.job.biz.WechatAuthBiz;
import com.wosai.upay.job.constant.ContractRuleConstants;
import com.wosai.upay.job.enume.TaskStatus;
import com.wosai.upay.job.mapper.ContractTaskMapper;
import com.wosai.upay.job.model.ContractTask;
import com.wosai.upay.job.model.DO.MerchantProviderParams;
import com.wosai.upay.job.model.RuleGroup;
import com.wosai.upay.job.model.UnionPayOpenStatusQueryResp;
import com.wosai.upay.job.model.alipay.AlipayMchInfo;
import com.wosai.upay.job.model.wechatAuth.WxMchInfo;
import com.wosai.upay.job.refactor.dao.MerchantProviderParamsDAO;
import com.wosai.upay.job.refactor.model.entity.MerchantProviderParamsDO;
import com.wosai.upay.job.util.ProviderUtil;
import com.wosai.upay.merchant.contract.model.provider.GuangFaParam;
import com.wosai.upay.merchant.contract.model.weixin.MchInfo;
import com.wosai.upay.merchant.contract.service.GuangFaService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;

/**
 * <AUTHOR>
 * @date 2021-04-16
 */
@Component("cgb-biz")
@Slf4j
public class CgbAcquirerBiz implements IAcquirerBiz {
    @Autowired
    ContractParamsBiz contractParamsBiz;
    @Autowired
    private GuangFaService guangFaService;
    @Autowired
    private WechatAuthBiz wechatAuthBiz;

    @Autowired
    private MerchantProviderParamsDAO merchantProviderParamsDAO;

    @Autowired
    private ContractTaskMapper contractTaskMapper;

    @Autowired
    private RuleContext ruleContext;

    @Override
    public String getDefaultRuleGroup(String acquirer) {
        return McConstant.RULE_GROUP_CGB;
    }

    @Override
    public String getAcquirerMchIdFromMerchantConfig(Map merchantConfig) {
        return BeanUtil.getPropString(merchantConfig, "params.cgbbank_trade_params.provider_mch_id");
    }


    @Override
    public String getNormalWxRule() {
        return ContractRuleConstants.CGB_NORMAL_WEIXIN_RULE;
    }

    @Override
    public WxMchInfo getWxMchInfo(MerchantProviderParams providerParams) {
        String merchantName = wechatAuthBiz.getWechatAuthMerchantName(providerParams.getMerchant_sn());
        WxMchInfo wxMchInfo = new WxMchInfo().setMchInfo(new MchInfo().setMerchant_name(merchantName));
        try {
            GuangFaParam param = contractParamsBiz.buildContractParams(String.valueOf(providerParams.getProvider()), providerParams.getPayway(), providerParams.getChannel_no(), GuangFaParam.class);
            wxMchInfo.setSubdevConfig(guangFaService.queryWechatSubDevConfig(param, providerParams.getId()));
        }catch (Exception e){
            //不一定能找到 依赖于 merchant_config
        }
        return wxMchInfo;
    }

    @Override
    public AlipayMchInfo getAlipayMchInfo(MerchantProviderParams providerParams) {
        return new AlipayMchInfo().setName(null).setSub_merchant_id(providerParams.getPay_merchant_id());
    }

    @Override
    public UnionPayOpenStatusQueryResp queryUnionPayOpenStatus(String merchantSn) {
        Optional<MerchantProviderParamsDO> unionParam = merchantProviderParamsDAO.getMerchantProviderParamsByProviderAndPayway(merchantSn, ProviderEnum.PROVIDER_CGB.getValue(), PaywayEnum.UNIONPAY.getValue());
        if (unionParam.isPresent()) {
            return UnionPayOpenStatusQueryResp.builder()
                    .status(UnionPayOpenStatusQueryResp.SUCCESS_OPEN)
                    .retry(false)
                    .build();
        }
        ContractTask netInTask = getNetInTask(merchantSn, AcquirerTypeEnum.CGB.getValue());
        if (Objects.nonNull(netInTask) && (TaskStatus.PROGRESSING.getVal().equals(netInTask.getStatus()) || TaskStatus.PENDING.getVal().equals(netInTask.getStatus()))) {
            return UnionPayOpenStatusQueryResp.builder()
                    .status(UnionPayOpenStatusQueryResp.NOT_OPEN)
                    .message("开通中，请稍后重试")
                    .retry(true)
                    .build();
        }
        if (Objects.nonNull(netInTask) && TaskStatus.FAIL.getVal().equals(netInTask.getStatus())) {
            return UnionPayOpenStatusQueryResp.builder()
                    .status(UnionPayOpenStatusQueryResp.FAIL_OPEN)
                    .message("开通失败")
                    .retry(false)
                    .build();
        }
        return UnionPayOpenStatusQueryResp.builder()
                .status(UnionPayOpenStatusQueryResp.NOT_OPEN)
                .message("暂时无法开通，联系销售支持")
                .retry(false)
                .build();
    }

    private ContractTask getNetInTask(String merchantSn, String acquirer) {
        List<ContractTask> contractTasks = contractTaskMapper.getContractsBySnAndType(merchantSn, ProviderUtil.CONTRACT_TYPE_INSERT);
        for (ContractTask contractTask : contractTasks) {
            RuleGroup ruleGroup = ruleContext.getRuleGroup(contractTask.getRule_group_id());
            if (ruleGroup.getAcquirer().equals(acquirer)) {
                return contractTask;
            }
        }
        return null;
    }

    @Override
    public void updateClearanceProvider(String merchantId) {

    }
}
