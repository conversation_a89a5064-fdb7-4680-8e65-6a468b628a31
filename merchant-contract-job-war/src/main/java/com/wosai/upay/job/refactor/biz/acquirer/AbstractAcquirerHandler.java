package com.wosai.upay.job.refactor.biz.acquirer;


import com.alibaba.fastjson.JSONArray;
import com.github.benmanes.caffeine.cache.Cache;
import com.github.benmanes.caffeine.cache.Caffeine;
import com.shouqianba.cua.enums.contract.FeeEffectiveTypeEnum;
import com.shouqianba.cua.enums.core.AcquirerOrgTypeEnum;
import com.shouqianba.cua.enums.core.AcquirerTradeTypeEnum;
import com.shouqianba.cua.enums.core.ClearTypeEnum;
import com.shouqianba.cua.enums.status.ValidStatusEnum;
import com.shouqianba.cua.utils.object.EnumUtils;
import com.wosai.upay.job.biz.ContractParamsBiz;
import com.wosai.upay.job.handlers.CommonEventHandler;
import com.wosai.upay.job.model.dto.response.CuaCommonResultDTO;
import com.wosai.upay.job.refactor.biz.settlement.AcquirerSupportSettlementBiz;
import com.wosai.upay.job.refactor.config.exception.JobRateLimitException;
import com.wosai.upay.job.refactor.dao.ContractSubTaskDAO;
import com.wosai.upay.job.refactor.dao.McAcquirerDAO;
import com.wosai.upay.job.refactor.dao.McRuleGroupDAO;
import com.wosai.upay.job.refactor.dao.MerchantProviderParamsDAO;
import com.wosai.upay.job.refactor.model.bo.AcquirerInfoBO;
import com.wosai.upay.job.refactor.model.bo.ContractGroupRuleVerifyResultBO;
import com.wosai.upay.job.refactor.model.bo.MerchantFeatureBO;
import com.wosai.upay.job.refactor.model.dto.NewMerchantContractResultRspDTO;
import com.wosai.upay.job.refactor.model.entity.McAcquirerDO;
import com.wosai.upay.job.refactor.model.entity.McRuleGroupDO;
import com.wosai.upay.job.refactor.model.entity.MerchantProviderParamsDO;
import com.wosai.upay.job.refactor.model.enums.DefaultStatusEnum;
import com.wosai.upay.job.refactor.model.enums.PaywayEnum;
import com.wosai.upay.job.refactor.service.impl.McRulesDecisionServiceImpl;
import com.wosai.upay.merchant.contract.exception.ContractBizException;
import com.wosai.upay.merchant.contract.model.ContractResponse;
import com.wosai.upay.merchant.contract.model.provider.UnionAlipayParam;
import com.wosai.upay.merchant.contract.model.provider.UnionWeixinParam;
import com.wosai.upay.merchant.contract.service.NewUnionService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.retry.annotation.Retryable;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;


/**
 * 抽象的收单机构处理器
 * 复用共有的逻辑
 *
 * <AUTHOR>
 * @date 2023/11/28 10:07
 */
@Slf4j
public abstract class AbstractAcquirerHandler implements AcquirerSharedAbility {

    @Resource
    protected AcquirerCommonTemplate acquirerCommonTemplate;

    @Resource
    protected ContractSubTaskDAO contractSubTaskDAO;

    @Resource
    private AcquirerSupportSettlementBiz acquirerSupportSettlementBiz;

    @Resource
    protected McAcquirerDAO mcAcquirerDAO;

    @Resource
    private McRuleGroupDAO mcRuleGroupDAO;

    @Resource(type = McRulesDecisionServiceImpl.class)
    private McRulesDecisionServiceImpl mcRulesDecisionService;

    @Resource
    private MerchantProviderParamsDAO merchantProviderParamsDAO;

    @Resource
    private NewUnionService newUnionService;

    @Resource
    private ContractParamsBiz contractParamsBiz;

    private static final Integer INITIAL_CAPACITY = 50;

    private static final Integer MAXIMUM_SIZE = 400;

    private static final Long DEFAULT_DURATION = 5L;

    private final Cache<String, AcquirerInfoBO> acquirerInfoCache =
            Caffeine.newBuilder()
                    .initialCapacity(INITIAL_CAPACITY)
                    .maximumSize(MAXIMUM_SIZE)
                    .expireAfterWrite(DEFAULT_DURATION, TimeUnit.MINUTES).build();

    /**
     * 获取收单机构信息
     */
    @Override
    public AcquirerInfoBO getAcquirerInfo() {
        return acquirerInfoCache.get(getTypeValue(), this::doGetAcquirerInfo);
    }

    private AcquirerInfoBO doGetAcquirerInfo(String acquirer) {
        McAcquirerDO acquirerDO = mcAcquirerDAO.getByAcquirer(acquirer);
        AcquirerInfoBO acquirerInfoBO = new AcquirerInfoBO();
        acquirerInfoBO.setAcquirer(getTypeValue());
        if (acquirerDO != null) {
            acquirerInfoBO.setName(acquirerDO.getName());
            acquirerInfoBO.setProvider(acquirerDO.getProvider());
            acquirerInfoBO.setTradeType(EnumUtils.ofNullable(AcquirerTradeTypeEnum.class, acquirerDO.getTradeType()).orElse(null));
            acquirerInfoBO.setClearType(EnumUtils.ofNullable(ClearTypeEnum.class, acquirerDO.getClearType()).orElse(null));
            acquirerInfoBO.setOrgType(EnumUtils.ofNullable(AcquirerOrgTypeEnum.class, acquirerDO.getType()).orElse(null));
            acquirerInfoBO.setFeeEffectiveType(EnumUtils.ofNullable(FeeEffectiveTypeEnum.class, acquirerDO.getFeeEffectiveType()).orElse(null));
            if (StringUtils.isNotBlank(acquirerDO.getMetadata())) {
                List<AcquirerInfoBO.ProviderBO> maps = JSONArray.parseArray(acquirerDO.getMetadata(), AcquirerInfoBO.ProviderBO.class);
                List<String> providers = maps.stream().map(AcquirerInfoBO.ProviderBO::getProvider).collect(Collectors.toList());
                acquirerInfoBO.setSupportedProviders(providers);
            }
            return acquirerInfoBO;
        }
        return acquirerInfoBO;
    }


    @Override
    public Optional<String> getDefaultContractRuleGroupId() {
        List<String> mcRuleGroups = mcRuleGroupDAO.listRuleGroup(getTypeValue(), "新增进件", ValidStatusEnum.VALID.getValue(), DefaultStatusEnum.DEFAULT.getValue())
                .stream().map(McRuleGroupDO::getGroupId)
                .filter(StringUtils::isNotBlank).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(mcRuleGroups)) {
            return Optional.empty();
        }
        if (mcRuleGroups.size() == 1) {
            return Optional.of(mcRuleGroups.get(0));
        }
        log.warn("收单机构{}存在多个默认的报备规则组,默认取收单机构标识同名,其次取第一个", getTypeValue());
        for (String groupId : mcRuleGroups) {
            if (groupId.equals(getTypeValue())) {
                return Optional.of(groupId);
            }
        }
        return Optional.of(mcRuleGroups.get(0));
    }


   public String getMicroUpdageDefaultContractRuleGroupId() {
        throw new ContractBizException("不支持小微升级");
   }

    /**
     * 根据商户特征,判断是否符合收单机构进件规则
     * 特殊处理子类完成acquirerSpecificCheck()方法
     *
     * @param merchantFeatureBO   商户特征
     * @return 校验结果
     */
    @Override
    public ContractGroupRuleVerifyResultBO checkSatisfactionToAcquirerTemplate(MerchantFeatureBO merchantFeatureBO) {
        ContractGroupRuleVerifyResultBO verifyResultBO = satisfactionToAcquirerSpecificCheck(merchantFeatureBO);
        if (!verifyResultBO.isCheckPass()) {
            return verifyResultBO;
        }
        return commonCheck(merchantFeatureBO);
    }

    /**
     * 收单机构公共校验
     *
     * @param merchantFeatureBO   商户特征
     * @return 校验结果
     */
    private ContractGroupRuleVerifyResultBO commonCheck(MerchantFeatureBO merchantFeatureBO) {
        String acquirer = getType().getValue();
        return acquirerSupportSettlementBiz.verifySettlementAccount(merchantFeatureBO, acquirer);
    }

    /**
     * 切至对应收单机构 对应收单机构的特殊校验
     * todo 后续把禁止类规则移到该逻辑
     *
     * @param merchantFeatureBO   商户特征
     * @return 校验结果
     */
    protected ContractGroupRuleVerifyResultBO satisfactionToAcquirerSpecificCheck(MerchantFeatureBO merchantFeatureBO) {
        return new ContractGroupRuleVerifyResultBO(true);
    }


    @Override
    public ContractGroupRuleVerifyResultBO checkMerchantEligibilityToAcquirer(String merchantSn) {
        return mcRulesDecisionService.checkMerchantEligibilityToAcquirer(merchantSn, getType().getValue());
    }


    /**
     * 更新支付源(微信支付宝)在银联侧的银联商户号
     *
     * @param paramsId              交易参数主键id
     * @param newUnionPayMerchantId 新的银联商户号
     * @return 更新结果
     */
    @Override
    @Retryable(value = {JobRateLimitException.class})
    public boolean updatePayUnionMerchantIdToUnion(String paramsId, String newUnionPayMerchantId) {
        Optional<MerchantProviderParamsDO> paramsOpt = merchantProviderParamsDAO.getByPrimaryKey(paramsId);
        if (!paramsOpt.isPresent()) {
            log.error("交易参数{}不存在", paramsId);
            return false;
        }
        MerchantProviderParamsDO merchantProviderParamsDO = paramsOpt.get();
        try {
            return doUpdatePayUnionMerchantId(merchantProviderParamsDO, newUnionPayMerchantId);
        } catch (JobRateLimitException e) {
            throw e;
        } catch (Exception e) {
            log.error("商户更新支付源银联商户号失败,merchantSn={},payMerchantId={},unionMerchantId={}",
                    merchantProviderParamsDO.getMerchantSn(), merchantProviderParamsDO.getPayMerchantId(), newUnionPayMerchantId, e);
            return false;
        }
    }

    /**
     * 执行更新支付源银联商户号的具体业务逻辑
     *
     * @param merchantProviderParamsDO 商户交易参数
     * @param newUnionPayMerchantId    新的银联商户号
     * @return 更新结果
     */
    private boolean doUpdatePayUnionMerchantId(MerchantProviderParamsDO merchantProviderParamsDO, String newUnionPayMerchantId) {
        ContractResponse contractResponse = doUpdatePayUnionMerchantIdToUnion(merchantProviderParamsDO, newUnionPayMerchantId);
        if (Objects.isNull(contractResponse)) {
            return false;
        }
        if (contractResponse.isSuccess()) {
            return true;
        }
        String message = contractResponse.getMessage();
        if (StringUtils.isNotBlank(message) && 
            (message.contains("包含调用频率超限") || message.contains("应用调用次数超限"))) {
            throw new JobRateLimitException("调用频率超限: " + message);
        }
        log.error("商户更新支付源银联商户号失败,merchantSn={},payMerchantId={},unionMerchantId={},message={}",
                merchantProviderParamsDO.getMerchantSn(), merchantProviderParamsDO.getPayMerchantId(), 
                newUnionPayMerchantId, message);
        return false;
    }

    private ContractResponse doUpdatePayUnionMerchantIdToUnion(MerchantProviderParamsDO merchantProviderParamsDO, String newUnionPayMerchantId) {
        if (Objects.equals(merchantProviderParamsDO.getPayway(), PaywayEnum.ALIPAY.getValue())) {
            UnionAlipayParam alipayParam = contractParamsBiz.buildContractParams(String.valueOf(merchantProviderParamsDO.getProvider()),
                    merchantProviderParamsDO.getPayway(), merchantProviderParamsDO.getChannelNo(), UnionAlipayParam.class);
            return newUnionService.updateAlipayUnionMerchantIdWithParams(merchantProviderParamsDO.getPayMerchantId(), newUnionPayMerchantId,alipayParam);
        } else if (Objects.equals(merchantProviderParamsDO.getPayway(), PaywayEnum.WEI_XIN.getValue())) {
            UnionWeixinParam unionWeixinParam = contractParamsBiz.buildContractParams(String.valueOf(merchantProviderParamsDO.getProvider()),
                    merchantProviderParamsDO.getPayway(), merchantProviderParamsDO.getChannelNo(), UnionWeixinParam.class);
            return newUnionService.updateWechatUnionMerchantId(merchantProviderParamsDO.getPayMerchantId(), newUnionPayMerchantId, unionWeixinParam);
        } else {
            log.error("商户:{}, 支付源{}不支持更新银联商户号", merchantProviderParamsDO.getMerchantSn(), merchantProviderParamsDO.getPayway());
            throw new ContractBizException("支付源不支持更新银联商户号");
        }
    }

    /**
     * 获取银联商户号
     *
     * @param unionPayParamsDO 云闪付交易参数 payWay=17
     * @param acquirerParamsDO 收单机构交易参数 payWay=0
     * @return 银联商户号
     */
    @Override
    public String getUnionMerchantId(MerchantProviderParamsDO unionPayParamsDO, MerchantProviderParamsDO acquirerParamsDO) {
        if (Objects.nonNull(unionPayParamsDO) && Objects.equals(unionPayParamsDO.getPayway(), com.shouqianba.cua.enums.core.PaywayEnum.UNIONPAY.getValue())) {
            return unionPayParamsDO.getPayMerchantId();
        }
        if (Objects.nonNull(acquirerParamsDO) && Objects.equals(acquirerParamsDO.getPayway(), com.shouqianba.cua.enums.core.PaywayEnum.ACQUIRER.getValue())) {
            return acquirerParamsDO.getProviderMerchantId();
        }
        return "";
    }

    @Override
    public CuaCommonResultDTO canChangeToOtherAcquirer(String merchantSn) {
        return CuaCommonResultDTO.success();
    }

    @Autowired
    protected CommonEventHandler commonEventHandler;

    @Override
    public NewMerchantContractResultRspDTO contractToAcquirerWithAtu(String merchantSn, Map<String, Object> contextParam) {
        final NewMerchantContractResultRspDTO rspDTO = new NewMerchantContractResultRspDTO();
        rspDTO.setStatus(NewMerchantContractResultRspDTO.STATUS_PROCESSING);
        rspDTO.setMessage("小微升级生成入网任务");
        try {
            final Long taskId = commonEventHandler.handleMicroUpgradeContractInsertTask(
                    merchantSn,
                    contextParam,
                    getMicroUpdageDefaultContractRuleGroupId()
            );
            rspDTO.setTaskId(taskId);
        } catch (Exception e) {
            log.error("商户:{}小微升级重新入网收单机构生成任务失败", merchantSn, e);
            rspDTO.setStatus(NewMerchantContractResultRspDTO.STATUS_FAIL);
            rspDTO.setMessage("小微升级生成入网任务失败");
        }
        return rspDTO;
    }
}
