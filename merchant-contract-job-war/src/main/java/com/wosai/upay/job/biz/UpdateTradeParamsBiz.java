package com.wosai.upay.job.biz;

import com.wosai.data.bean.BeanUtil;
import com.wosai.upay.core.service.TradeConfigService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Component;

import java.util.Map;
import java.util.Objects;
import java.util.Random;

/**
 * 更新merchant_config中的params字段
 *    和merchant_contract中updateTlParams做法一致
 * 原因：merchant_config.params是一个text字段。
 *     多通道入网的时候可能通联和拉卡拉同时成功，会同时调用接口去更新params里面的值
 *     core-b做法是取出params,然后把新增的key加进去，然后更新，会出现更新丢失的问题(第一次更新的数据被第二次更新的数据覆盖了)
 * 解决：
 *    更新完成后检查是否更新成功，没更新成功再更新一遍
 * <AUTHOR>
 * @date 2022/3/29
 */
@Slf4j
@Component
public class UpdateTradeParamsBiz {

    private static final Random RANDOM = new Random();

    @Autowired
    private TradeConfigService tradeConfigService;
    @Autowired
    @Qualifier("updateTradeParamsThreadPoolTaskExecutor")
    private ThreadPoolTaskExecutor updateTradeParamsThreadPoolTaskExecutor;


    public void updateLklTradeParams(String merchantId, Map params) {
        tradeConfigService.updateLakalaTradeParams(merchantId, params);
        updateTradeParamsThreadPoolTaskExecutor.execute(() -> {
            Map merchantConfig = tradeConfigService.getMerchantConfigByMerchantIdAndPayway(merchantId, null);
            if (Objects.isNull(BeanUtil.getNestedProperty(merchantConfig, "params.lakala_trade_params"))) {
                tradeConfigService.updateLakalaTradeParams(merchantId, params);
            }
        });
    }

    public void updateClearanceProvider(String merchantId, int clearanceProvider) {
        if (clearanceProvider == 0) {
            return;
        }
        tradeConfigService.updateClearanceProvider(merchantId, clearanceProvider);
        updateTradeParamsThreadPoolTaskExecutor.execute(() -> {
            try {
                Thread.sleep(RANDOM.nextInt(200) + 50L);
            } catch (Exception e) {
                log.error("sleep失败", e);
            }
            tradeConfigService.updateClearanceProvider(merchantId, clearanceProvider);
        });
    }


}
