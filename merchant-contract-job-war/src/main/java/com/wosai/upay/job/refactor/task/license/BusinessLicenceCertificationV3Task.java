package com.wosai.upay.job.refactor.task.license;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.shouqianba.cua.enums.contract.AcquirerTypeEnum;
import com.shouqianba.cua.enums.contract.AffectPrimaryTaskStatusEnum;
import com.shouqianba.cua.enums.core.PaywayEnum;
import com.shouqianba.cua.enums.status.ValidStatusEnum;
import com.shouqianba.cua.utils.object.DateExtensionUtils;
import com.shouqianba.cua.utils.object.StringExtensionUtils;
import com.shouqianba.service.MerchantContractService;
import com.wosai.data.dao.DaoConstants;
import com.wosai.data.util.CollectionUtil;
import com.wosai.mc.model.MerchantInfo;
import com.wosai.mc.service.MerchantBusinessLicenseService;
import com.wos極速
<content>
// ... (original clean file content) ...

@Component
@Slf4j
public class BusinessLicenceCertificationV3Task extends AbstractInternalScheduleTaskHandleTemplate {

    // ... (existing fields) ...

    @Resource
    private InterScheduleTaskServiceImpl interScheduleTaskService;

    @Resource
    private InternalScheduleMainTaskDAO internalScheduleMainTaskDAO;

    // IMPORTANT: Add the required DAO
    @Resource
    private InternalScheduleSubTaskDAO internalScheduleSubTaskDAO;

    // ... (keep all other methods unchanged until doInsertTaskByLicenseAuditApply) ...

    public void doInsertTaskByLicenseAuditApply(String merchantSn, Integer fieldAppInfoId, BusinessLicenseAuditApplyDTO businessLicenseAuditApplyDTO) {
        boolean isMicroUpgrade = Boolean.TRUE;
        InternalScheduleMainTaskDO mainTask = buildMainTask(merchantSn, isMicroUpgrade, businessLicenseAuditApplyDTO, fieldAppInfoId);
        int priority = 0;
        int subTaskNum = 0;
        InternalScheduleSubTaskDO accountVerifySubTask = null;
        
        if (needUpdateBankAccount(businessLicenseAuditApplyDTO)) {
            accountVerifySubTask = buildAccountVerifyTask(mainTask);
            priority++;
            subTaskNum++;
        }
        
        List<InternalScheduleSubTaskDO> otherSubTasks = buildOtherSubTasks(isMicroUpgrade, businessLicenseAuditApplyDTO, mainTask, priority);
        subTaskNum = subTaskNum + (int) otherSubTasks.stream()
                .filter(t -> Objects.equals(t.getAffectMainTaskStatus(), AffectPrimaryTaskStatusEnum.YES.getValue()))
                .count();
        mainTask.setAffectStatusSubTaskNum(subTaskNum);
        
        // Create a list for all sub-tasks
        List<InternalScheduleSubTaskDO> allSubTasks = new ArrayList<>();
        if (accountVerifySubTask != null) {
            allSubTasks.add(accountVerifySubTask);
        }
        allSubTasks.addAll(otherSubTasks);

        // Disable scheduling initially
        mainTask.setEnableScheduledStatus(ScheduleEnum.SCHEDULE_DISABLE.getValue());
        
        // Insert all at once
        interScheduleTaskService.insertTasksWithExpireTime(mainTask, allSubTasks);
        
        // Now set dependencies
        List<InternalScheduleSubTaskDO> tasksToUpdate = new ArrayList<>();
        
        // Find the current in-use reContract task
        InternalScheduleSubTaskDO currentReContractTask = allSubTasks.stream()
            .filter(task -> SUB_TASK_TYPE_RE_CONTRACT.equals(task.getTaskType()) && 
                      mainTask.getAcquirer().equals(task.getAcquirer()))
            .findFirst()
            .orElse(null);
        
        // Set account verification dependency for current reContract task
        if (accountVerifySubTask != null && currentReContractTask != null) {
            currentReContractTask.setDependOnSubTaskId(accountVerifySubTask.getId());
            tasksToUpdate.add(currentReContractTask);
        }
        
        // Set dependencies for other reContract tasks
        for (InternalScheduleSubTaskDO task : allSubTasks) {
            if (SUB_TASK_TYPE_RE_CONTRACT.equals(task.getTaskType()) && task != currentReContractTask && currentReContractTask != null) {
                task.setDependOnSubTaskId(currentReContractTask.getId());
                tasksToUpdate.add(task);
            }
        }
        
        // Set dependencies for auth task
        Optional<InternalScheduleSubTaskDO> authTaskOpt = allSubTasks.stream()
            .filter(task -> SUB_TASK_TYPE_PAYWAY_AUTH.equals(task.getTaskType()))
            .findFirst();
        
        if (authTaskOpt.isPresent()) {
            InternalScheduleSubTaskDO authTask = authTaskOpt.get();
            // Collect all reContract task IDs
            String reContractIds = allSubTasks.stream()
                .filter(t -> SUB_TASK_TYPE_RE_CONTRACT.equals(t.getTaskType()))
                .map(t -> t.getId().toString())
                .collect(Collectors.joining(","));
            authTask.setDependOnSubTaskId(reContractIds);
            tasksToUpdate.add(authTask);
        }
        
        // Set dependencies for change params task
        Optional<InternalScheduleSubTaskDO> changeParamsTaskOpt = allSubTasks.stream()
            .filter(task -> SUB_TASK_TYPE_CHANGE_PARAMS.equals(task.getTaskType()))
            .findFirst();
            
        if (changeParamsTaskOpt.isPresent() && authTaskOpt.isPresent()) {
            changeParamsTaskOpt.get().setDependOnSubTaskId(authTaskOpt.get().getId());
            tasksToUpdate.add(changeParamsTaskOpt.get());
        }
        
        // Update tasks with dependencies
        if (!tasksToUpdate.isEmpty()) {
            internalScheduleSubTaskDAO.batchUpdateDependencies(tasksToUpdate);
        }
        
        // Enable scheduling
        mainTask.setEnableScheduledStatus(ScheduleEnum.SCHEDULE_ENABLE.getValue());
        internalScheduleMainTaskDAO.updateById(mainTask);
    }

    // ... (rest of the class remains unchanged) ...
}
