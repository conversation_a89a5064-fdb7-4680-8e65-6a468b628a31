package com.wosai.upay.job.providers;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.shouqianba.cua.enums.contract.AcquirerTypeEnum;
import com.shouqianba.cua.enums.contract.ProviderEnum;
import com.shouqianba.cua.enums.core.PaywayEnum;
import com.wosai.common.utils.WosaiMapUtils;
import com.wosai.common.utils.WosaiStringUtils;
import com.wosai.data.bean.BeanUtil;
import com.wosai.data.util.CollectionUtil;
import com.wosai.mc.model.MerchantInfo;
import com.wosai.mc.model.StoreInfo;
import com.wosai.mc.service.StoreService;
import com.wosai.trade.service.result.ListMchFeeRateResult;
import com.wosai.upay.common.bean.ListResult;
import com.wosai.upay.common.dao.DaoConstants;
import com.wosai.upay.common.helper.MyObjectMapper;
import com.wosai.upay.core.model.*;
import com.wosai.upay.job.biz.*;
import com.wosai.upay.job.biz.paramContext.ParamContextBiz;
import com.wosai.upay.job.enume.*;
import com.wosai.upay.job.exception.ContextParamException;
import com.wosai.upay.job.model.*;
import com.wosai.upay.job.model.DO.MerchantProviderParams;
import com.wosai.upay.job.model.DO.MerchantProviderParamsExample;
import com.wosai.upay.job.service.ContractTaskServiceImpl;
import com.wosai.upay.job.util.CommonUtil;
import com.wosai.upay.job.util.ProviderUtil;
import com.wosai.upay.merchant.contract.constant.Constant;
import com.wosai.upay.merchant.contract.exception.ContractBizException;
import com.wosai.upay.merchant.contract.model.ContractResponse;
import com.wosai.upay.merchant.contract.model.MerchantProviderTradeParams;
import com.wosai.upay.merchant.contract.model.WeixinConfig;
import com.wosai.upay.merchant.contract.model.provider.TongLianV2Param;
import com.wosai.upay.merchant.contract.model.terminal.AddTermInfoDTO;
import com.wosai.upay.merchant.contract.model.terminal.LogOutTermInfoDTO;
import com.wosai.upay.merchant.contract.model.terminal.UpdateTermInfoDTO;
import com.wosai.upay.merchant.contract.service.TongLianService;
import com.wosai.upay.merchant.contract.service.TongLianV2Service;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.MapUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.text.DecimalFormat;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @Description: 通联收银宝
 * <AUTHOR>
 * @Date 2023/6/8 13:36
 **/

@Slf4j
@Component(ProviderUtil.TONGLIAN_V2_CHANNEL)
public class TongLianV2Provider extends TongLianProvider {

    @Autowired
    TongLianV2Service tongLianV2Service;

    @Autowired
    TongLianService tongLianService;

    @Autowired
    ProviderTerminalBiz providerTerminalBiz;

    @Autowired
    RuleContext ruleContext;

    @Autowired
    ContractParamsBiz contractParamsBiz;

    @Autowired
    StoreService storeService;

    @Autowired
    IndustryMappingCommonBiz industryMappingCommonBiz;

    private static String PID_FIELD = "pid";
    private static String PID_VALUE = "P0003";
    private static String MTRXCODE_FIELD = "mtrxcode";
    private static String MTRXCODE_VALUE_ALIPAY = "VSP511";
    private static String MTRXCODE_VALUE_WECHAT = "VSP501";
    private static String MTRXCODE_VALUE_UNIONPAY = "VSP551";
    private static String FEERATE_FIELD = "feerate";
    private static String CREDITFEERATE_FIELD = "creditrate";
    private static String RANKFEEARR_FIELD = "rankfeearr";
    private static String RANK_FIELD = "rank";


    // 进件状态常量 ---------------
    private static String CONTRACT_SUCCESS = "02";
    private static String CONTRACT_FREEZE = "06";
    private static String CONTRACT_SUBMIT_FAIL = "04";
    private static String CONTRACT_AUDIT_FAIL = "03";

    // 无纸化签约状态常量 ---------------
    private static String SIGN_SUCCESS = "0";
    private static String SIGN_FAIL = "1";
    private static String SIGN__DISABLED = "4";


    @Override
    String getProviderBeanName() {
        return ProviderUtil.TONGLIAN_V2_CHANNEL;
    }

    @Override
    protected ContractSubTask produceUpdateTaskByRule(String merchantSn, ContractEvent event, Map<String, Object> paramContext, ContractRule contractRule) {
        String acquirer = acquirerBiz.getMerchantAcquirer(merchantSn);
        // 非当前收单机构，更新不影响总状态
//        int influPtask = McConstant.ACQUIRER_TONGLIANV2.equals(acquirer) ? contractRule.getUpdateInfluPtask() : 0;
        int influPtask = AcquirerTypeEnum.TONG_LIAN_V2.getValue().equals(acquirer) || isSubBiz(merchantSn, AcquirerTypeEnum.TONG_LIAN_V2.getValue()) ? contractRule.getUpdateInfluPtask() : 0;

        ContractSubTask subTask = new ContractSubTask()
                .setMerchant_sn(merchantSn)
                .setStatus_influ_p_task(influPtask)
                .setChannel(ProviderUtil.TONGLIAN_V2_CHANNEL)
                .setChange_config(0)
                .setDefault_channel(contractRule.getDefault())
                .setPayway(contractRule.getPayway())
                .setContract_rule(contractRule.getRule())
                .setRule_group_id(event.getRule_group_id())
                .setSchedule_status(ScheduleEnum.SCHEDULE_ENABLE.getValue())
                .setRetry(0);

        Integer taskType = null;
        //银行账户变更
        if (ContractEvent.OPT_TYPE_MERCHANT_SETTLEMENT_ACCOUNTS == event.getEvent_type()) {
            if (contractRule.getPayway() == null || PaywayEnum.ACQUIRER.getValue().equals(contractRule.getPayway())) {
                Map requestParam = (Map) paramContext.get("cardRequestParam");
                if (!CollectionUtils.isEmpty(requestParam)) {
                    //银行卡管理服务发起的变更(merchant_bank_account_pre)
                    taskType = ProviderUtil.SUB_TASK_TASK_TYPE_CARD_UPDATE;
                } else {
                    //dts订阅直接变更(merchant_bank_account)
                    taskType = ProviderUtil.SUB_TASK_TASK_TYPE_SETTLEMENT_ACCOUNTS;
                }
            }
        } else if (ContractEvent.OPT_TYPE_MERCHANT_BASIC_INFORMATION == event.getEvent_type()) {
            //更新基本信息
            taskType = ProviderUtil.SUB_TASK_TASK_TYPE_BASIC_INFORMATION;
        } else if (ContractEvent.OPT_TYPE_NET_CRM_UPDATE == event.getEvent_type()) {
            String crmUpdate = BeanUtil.getPropString(paramContext, "crmUpdate");
            if (!StringUtils.isEmpty(crmUpdate)) {
                if ("0".equals(crmUpdate)) {
                    taskType = ProviderUtil.SUB_TASK_TASK_TYPE_BASIC_INFORMATION;
                } else if ("1".equals(crmUpdate)) {
                    taskType = ProviderUtil.SUB_TASK_TASK_TYPE_SETTLEMENT_ACCOUNTS;
                } else if ("2".equals(crmUpdate)) {
                    taskType = ProviderUtil.SUB_TASK_TASK_TYPE_CRM_BOTH;
                } else {
                    //do nothing
                }
            }

        } else if (ContractEvent.OPT_TYPE_UPDATE_BUSINESS_LICENSE == event.getEvent_type()) {
            if (PaywayEnum.ACQUIRER.getValue().equals(contractRule.getPayway())) {
                //更新营业执照
                taskType = ProviderUtil.SUB_TASK_TASK_TYPE_UPDATE_BUSINESS_LICENSE;
            }
        } else if (ContractEvent.OPT_TYPE_NET_IN == event.getEvent_type()) {
            taskType = ProviderUtil.SUB_TASK_TASK_TYPE_BASIC_INFORMATION;
        } else if (ContractEvent.OPT_TYPE_MERCHANT_FEERATE == event.getEvent_type()) {
            taskType = ProviderUtil.SUB_TASK_TASK_TYPE_MERCHANT_FEERATE;
        }

        if (taskType == null) {
            return null;
        }
        return subTask.setTask_type(taskType);
    }

    @Override
    protected ContractResponse processInsertTaskByRule(ContractTask contractTask, ContractChannel contractChannel, ContractSubTask sub) {
        Integer payWay = sub.getPayway();
        Map contextParam = JSON.parseObject(contractTask.getEvent_context(), Map.class);
        if (PaywayEnum.ACQUIRER.getValue().equals(payWay)) {
            TongLianV2Param tongLianV2Param = buildParam(contractChannel, sub, TongLianV2Param.class);
            ContractRule contractRule = ruleContext.getContractRule(sub.getContract_rule());
            if (contractRule.getType() == 1) {
                Map bankAccount = (Map) contextParam.get(ParamContextBiz.KEY_BANK_ACCOUNT);
                if (!tongLianService.verifyThreeElements(bankAccount)) {
                    return ContractResponse.builder()
                            .code(460)
                            .message("三要素验证不通过")
                            .build();
                }
                if (needPayFor(contextParam, sub, contractTask)) {
                    return null;
                }
                ContractResponse response = tongLianV2Service.contractMerchant(contextParam, tongLianV2Param);
                String merchantId = BeanUtil.getPropString(contextParam, "merchant.id");
                updateClearProvider(merchantId, sub.getMerchant_sn());
                return response;
            } else if (contractRule.getType() == 3) {
                ContractSubTask depSubTask = contractSubTaskMapper.selectByPrimaryKey(sub.getSchedule_dep_task_id());
                ContractResponse response = tongLianV2Service.queryContractMerchantStatus(contractTask.getMerchant_sn(), BeanUtil.getPropString(depSubTask.getResponseBody(), "merchantid"), tongLianV2Param);
                String auditstatus = BeanUtil.getPropString(response.getResponseParam(), "auditstatus");
                if (CONTRACT_SUCCESS.equals(auditstatus) || CONTRACT_FREEZE.equals(auditstatus)) {
                    saveSignUrl(contractTask, tongLianV2Param);
                    return response;
                } else if (CONTRACT_SUBMIT_FAIL.equals(auditstatus) || CONTRACT_AUDIT_FAIL.equals(auditstatus)) {
                    return response.setCode(Constant.RESULT_CODE_BIZ_EXCEPTION).setMessage(BeanUtil.getPropString(response.getResponseParam(), "retmsg"));
                } else {
                    return response.setCode(Constant.RESULT_CODE_SYSTEM_EXCEPTION).setMessage(BeanUtil.getPropString(response.getResponseParam(), "retmsg"));
                }
            } else if (contractRule.getType() == 4) {
                ContractResponse response = tongLianV2Service.queryelectsignStatus(contractTask.getMerchant_sn(), tongLianV2Param);
                String auditstatus = BeanUtil.getPropString(response.getResponseParam(), "electsignstatus");
                if (SIGN_SUCCESS.equals(auditstatus)) {
                    return response;
                } else if (SIGN_FAIL.equals(auditstatus)) {
                    return response.setCode(Constant.RESULT_CODE_BIZ_EXCEPTION).setMessage(BeanUtil.getPropString(response.getResponseParam(), "retmsg"));
                } else if (SIGN__DISABLED.equals(auditstatus)) {
                    Calendar calendar = Calendar.getInstance();

                    // 设置为当天凌晨
                    calendar.setTime(contractTask.getCreate_at());
                    calendar.add(Calendar.MONTH, 1);
                    if (new Date().before(calendar.getTime())) {
                        tongLianV2Service.resendElectsignStatus(contractTask.getMerchant_sn(), tongLianV2Param);
                        return response.setCode(Constant.RESULT_CODE_SYSTEM_EXCEPTION).setMessage(BeanUtil.getPropString(response.getResponseParam(), "retmsg"));
                    } else {
                        return response.setCode(Constant.RESULT_CODE_BIZ_EXCEPTION).setMessage(BeanUtil.getPropString(response.getResponseParam(), "retmsg"));
                    }
                } else {
                    return response.setCode(Constant.RESULT_CODE_SYSTEM_EXCEPTION).setMessage(BeanUtil.getPropString(response.getResponseParam(), "retmsg"));
                }
            }
        } else if (PaywayEnum.WEIXIN.getValue().equals(payWay)) {
            TongLianV2Param param = buildParam(contractChannel, sub, TongLianV2Param.class);
            ContractResponse response = tongLianV2Service.queryPayWayInfo(contextParam, payWay, param);
            Map<String, Object> tradeParam = response.getTradeParam();
            MerchantProviderParams params = getAcquirerParams(contractTask.getMerchant_sn());
            if (response.isSuccess()) {
                Map respParam = response.getResponseParam();
                String payMerchantId = BeanUtil.getPropString(respParam, "cmid");
                MerchantProviderParams merchantProviderParams = merchantProviderParamsMapper.selectByPayMerchantId(payMerchantId);
                if (merchantProviderParams == null) {
                    String merchantName = getMerchantName(contractTask.getMerchant_sn());
                    String settlementId = BeanUtil.getPropString(respParam, "settid");
                    MerchantProviderParams newParams = new MerchantProviderParams().setId(UUID.randomUUID().toString()).setMerchant_name(merchantName)
                            .setOut_merchant_sn(params.getOut_merchant_sn()).setParent_merchant_id(param.getChannel_no())
                            .setChannel_no(param.getChannel_no()).setProvider(ProviderEnum.PROVIDER_TONGLIAN_V2.getValue()).setProvider_merchant_id(params.getProvider_merchant_id()).setPay_merchant_id(payMerchantId).setMerchant_sn(params.getMerchant_sn())
                            .setParams_config_status(MerchantProviderParams.PARAMS_CONFIG_STATUS_PRE).setContract_rule(sub.getContract_rule()).setRule_group_id(contractTask.getRule_group_id()).setPayway(PaywayEnum.WEIXIN.getValue())
                            .setWx_settlement_id(settlementId).setCtime(System.currentTimeMillis()).setMtime(System.currentTimeMillis());
                    merchantProviderParamsMapper.insertSelective(newParams);
                    merchantProviderParams = merchantProviderParamsMapper.selectByPayMerchantId(payMerchantId);
                }
                response.setMerchantProviderParamsId(merchantProviderParams.getId());
                tradeParam.put(MerchantProviderTradeParams.PROVIDER_MERCHANT_ID, params.getProvider_merchant_id());
                tradeParam.put(MerchantProviderTradeParams.WEIXIN_MERCHANT_ID, payMerchantId);
                response.setTradeParam(tradeParam);
            } else {
                response.setTradeParam(tradeParam);
            }
            return response;
        } else if (PaywayEnum.ALIPAY.getValue().equals(payWay)) {
            TongLianV2Param param = buildParam(contractChannel, sub, TongLianV2Param.class);
            ContractResponse response = tongLianV2Service.queryPayWayInfo(contextParam, payWay, param);
            Map<String, Object> tradeParam = response.getTradeParam();
            Map merchant = MapUtils.getMap(contextParam, ParamContextBiz.KEY_MERCHANT);
            MerchantProviderParamsExample dto = new MerchantProviderParamsExample();
            dto.createCriteria()
                    .andMerchant_snEqualTo(contractTask.getMerchant_sn())
                    .andPaywayEqualTo(PaywayEnum.ACQUIRER.getValue())
                    .andProviderEqualTo(ProviderEnum.PROVIDER_TONGLIAN_V2.getValue());
            MerchantProviderParams params = merchantProviderParamsMapper.selectByExample(dto).get(0);
            if (response.isSuccess()) {
                Map respParam = response.getResponseParam();
                String payMerchantId = BeanUtil.getPropString(respParam, "cmid");
                MerchantProviderParams merchantProviderParams = merchantProviderParamsMapper.selectByPayMerchantId(payMerchantId);
                if (merchantProviderParams == null) {
                    String unionMcc = industryMappingCommonBiz.getAliIndirectMcc(BeanUtil.getPropString(merchant, Merchant.INDUSTRY));
                    MerchantProviderParams newParams = new MerchantProviderParams().setId(UUID.randomUUID().toString()).setOut_merchant_sn(params.getOut_merchant_sn())
                            .setMerchant_name(getMerchantName(contractTask.getMerchant_sn())).setParent_merchant_id(param.getChannel_no())
                            .setChannel_no(param.getChannel_no()).setProvider(ProviderEnum.PROVIDER_TONGLIAN_V2.getValue()).setProvider_merchant_id(params.getProvider_merchant_id()).setPay_merchant_id(payMerchantId).setMerchant_sn(params.getMerchant_sn())
                            .setParams_config_status(MerchantProviderParams.PARAMS_CONFIG_STATUS_NULL).setContract_rule(sub.getContract_rule()).setRule_group_id(contractTask.getRule_group_id()).setPayway(PaywayEnum.ALIPAY.getValue())
                            .setAli_mcc(unionMcc).setCtime(System.currentTimeMillis()).setMtime(System.currentTimeMillis());
                    merchantProviderParamsMapper.insertSelective(newParams);
                    merchantProviderParams = merchantProviderParamsMapper.selectByPayMerchantId(payMerchantId);
                }
                response.setMerchantProviderParamsId(merchantProviderParams.getId());
                tradeParam.put(MerchantProviderTradeParams.PROVIDER_MERCHANT_ID, params.getProvider_merchant_id());
                tradeParam.put(MerchantProviderTradeParams.ALIPAY_MERCHANT_ID, payMerchantId);
                response.setTradeParam(tradeParam);
            } else {
                response.setTradeParam(tradeParam);
            }
            return response;
        } else if (PaywayEnum.UNIONPAY.getValue().equals(payWay)) {
            TongLianV2Param param = buildParam(contractChannel, sub, TongLianV2Param.class);
            ContractResponse response = tongLianV2Service.queryPayWayInfo(contextParam, payWay, param);
            Map<String, Object> tradeParam = response.getTradeParam();
            if (!response.isSuccess()) {
                return response;
            }
            //进件富友成功后的信息
            MerchantProviderParamsExample dto = new MerchantProviderParamsExample();
            dto.createCriteria()
                    .andMerchant_snEqualTo(contractTask.getMerchant_sn())
                    .andPaywayEqualTo(PaywayEnum.ACQUIRER.getValue())
                    .andProviderEqualTo(ProviderEnum.PROVIDER_TONGLIAN_V2.getValue());
            MerchantProviderParams params = merchantProviderParamsMapper.selectByExample(dto).get(0);
            //富友子商户号查询接口返回
            Map respParam = response.getResponseParam();
            String payMerchantId = BeanUtil.getPropString(respParam, "cmid");
            MerchantProviderParams merchantProviderParams = merchantProviderParamsMapper.selectByPayMerchantId(payMerchantId);
            if (merchantProviderParams == null) {
                //保存merchant_provider_params
                MerchantProviderParams newParams = new MerchantProviderParams().setId(UUID.randomUUID().toString()).setOut_merchant_sn(params.getOut_merchant_sn())
                        .setMerchant_name(getMerchantName(contractTask.getMerchant_sn())).setParent_merchant_id(params.getProvider_merchant_id())
                        .setChannel_no(param.getChannel_no()).setProvider(ProviderEnum.PROVIDER_TONGLIAN_V2.getValue()).setProvider_merchant_id(params.getProvider_merchant_id()).setPay_merchant_id(payMerchantId).setMerchant_sn(params.getMerchant_sn())
                        .setParams_config_status(MerchantProviderParams.PARAMS_CONFIG_STATUS_NULL).setContract_rule(sub.getContract_rule()).setRule_group_id(contractTask.getRule_group_id()).setPayway(PaywayEnum.UNIONPAY.getValue())
                        .setCtime(System.currentTimeMillis()).setMtime(System.currentTimeMillis());
                merchantProviderParamsMapper.insertSelective(newParams);
                merchantProviderParams = merchantProviderParamsMapper.selectByPayMerchantId(payMerchantId);
            }
            response.setMerchantProviderParamsId(merchantProviderParams.getId());
            tradeParam.put(MerchantProviderTradeParams.PROVIDER_MERCHANT_ID, params.getProvider_merchant_id());
            tradeParam.put(MerchantProviderTradeParams.UNIONOPEN_MERCHANT_ID, payMerchantId);
            response.setTradeParam(tradeParam);
            return response;
        }
        return null;
    }


    private void saveSignUrl(ContractTask contractTask, TongLianV2Param tongLianV2Param) {
        MerchantProviderParamsExample dto = new MerchantProviderParamsExample();
        dto.createCriteria()
                .andMerchant_snEqualTo(contractTask.getMerchant_sn())
                .andPaywayEqualTo(PaywayEnum.ACQUIRER.getValue())
                .andProviderEqualTo(ProviderEnum.PROVIDER_TONGLIAN_V2.getValue());
        List<MerchantProviderParams> paramsList = merchantProviderParamsMapper.selectByExample(dto);
        if (!CollectionUtils.isEmpty(paramsList)) {
            MerchantProviderParams params = paramsList.get(0);
            Map paramsExtra = CommonUtil.bytes2Map(params.getExtra());
            ContractResponse electUrlResponse = tongLianV2Service.queryElectUrl(contractTask.getMerchant_sn(), tongLianV2Param);
            if (electUrlResponse.isSuccess()) {
                Map respParam = electUrlResponse.getResponseParam();
                paramsExtra.put("signUrl", BeanUtil.getPropString(respParam, "sybsignurl"));
                merchantProviderParamsMapper.updateByPrimaryKeySelective(new MerchantProviderParams().setExtra(CommonUtil.map2Bytes(paramsExtra)).setId(params.getId()));
            }
        }
    }

    @Override
    protected ContractResponse processUpdateTaskByRule(ContractTask contractTask, ContractChannel contractChannel, ContractSubTask sub) {
        Map contextParam = JSON.parseObject(contractTask.getEvent_context(), Map.class);
        if (ProviderUtil.CONTRACT_TYPE_UPDATE_BANK_ACCOUNT.equals(contractTask.getType())) {
            Map bankAccount = (Map) contextParam.get(ParamContextBiz.KEY_BANK_ACCOUNT);
            String bankName = MapUtils.getString(bankAccount, MerchantBankAccount.BANK_NAME);
            /**
             * 银行业务开通成功以后需要将最新的银行卡同步到间连,此时可能由于三方数据不及时导致三要素校验不通过,所以此时不校验三要素,但是有的银行业务开通的时候没有银行卡,
             * 所以又很难根据银行卡相关信息判断,因此只能根据设置默认银行卡的特征affect_sub_task_count=0和status=5来判断
             * @see ContractTaskServiceImpl#createSuccessTask(java.lang.String)
             */
            final Integer status = contractTask.getStatus();
            final Integer affectSubTaskCount = contractTask.getAffect_sub_task_count();
            if (hxBankName.equals(bankName) || (Objects.equals(status, TaskStatus.SUCCESS.getVal()) && Objects.equals(affectSubTaskCount, 0))) {
                //华夏银行不做通联的三要素校验
                //开通银行直连不需要做三要素校验,因此不需要操作
            } else if (!tongLianService.verifyThreeElements(bankAccount)) {
                return ContractResponse.builder()
                        .code(460)
                        .message("三要素验证不通过")
                        .build();
            }
            if (needPayFor(contextParam, sub, contractTask)) {
                return null;
            }
        }
        Integer subTaskType = sub.getTask_type();
        TongLianV2Param tongLianV2Param = contractParamsBiz.buildContractParamsByContractSubTask(sub, TongLianV2Param.class);
        if (ProviderUtil.SUB_TASK_TASK_TYPE_SETTLEMENT_ACCOUNTS.equals(subTaskType) ||
                ProviderUtil.SUB_TASK_TASK_TYPE_CARD_UPDATE.equals(subTaskType)) {
            return tongLianV2Service.updateMerchantBankAccount(contextParam, tongLianV2Param);
        } else if (ProviderUtil.SUB_TASK_TASK_TYPE_UPDATE_BUSINESS_LICENSE.equals(subTaskType) || ProviderUtil.SUB_TASK_TASK_TYPE_BASIC_INFORMATION.equals(subTaskType)) {
            //变更营业执照 与 变更商户信息相同
            return tongLianV2Service.updateMerchantBasic(contextParam, tongLianV2Param);
        } else if (ProviderUtil.SUB_TASK_TASK_TYPE_MERCHANT_FEERATE.equals(subTaskType)) {
            return tongLianV2Service.updateMerchantFeerate(contextParam, tongLianV2Param);
        } else if (ProviderUtil.SUB_TASK_TASK_TYPE_STATUS_UPDATE.equals(subTaskType)) {
            Integer status = MapUtils.getInteger(MapUtils.getMap(contextParam, "merchant"), Merchant.STATUS);
            return tongLianV2Service.updateMerchantStatus(contractTask.getMerchant_sn(), status, tongLianV2Param);
        } else {
            return tongLianV2Service.updateMerchantBasic(contextParam, tongLianV2Param);
        }

    }

    /**
     * 通联收银宝 若存在一个即算作已经入网
     *
     * @param event
     * @param contractRule
     * @return
     */
    @Override
    protected boolean hasContract(ContractEvent event, ContractRule contractRule) {
        MerchantProviderParamsExample example = new MerchantProviderParamsExample();
        example.or().andMerchant_snEqualTo(event.getMerchant_sn())
                .andPaywayEqualTo(contractRule.getPayway())
                .andProviderEqualTo(Integer.valueOf(contractRule.getProvider()))
                .andDeletedEqualTo(false);
        return merchantProviderParamsMapper.countByExample(example) > 0;
    }

    @Override
    public ContractResponse boundTerminal(AddTermInfoDTO termInfoDTO, int payWay, String terminalSn) {
        TongLianV2Param tongLianV2Param = contractParamsBiz.buildContractParamsByPayMchId(termInfoDTO.getSubMchId(), TongLianV2Param.class);
        return tongLianV2Service.queryTerm(termInfoDTO, tongLianV2Param, payWay);
    }

    @Override
    public ContractResponse unbindTerminal(LogOutTermInfoDTO dto, int payWay, String terminalSn) {
        MerchantProviderParams params = getAcquirerParams(dto.getMerchantSn());
        dto.setSubMchId(params.getPay_merchant_id());
        TongLianV2Param tongLianV2Param = contractParamsBiz.buildContractParamsByParams(params, TongLianV2Param.class);
        return tongLianV2Service.logOutTerm(dto, tongLianV2Param);
    }

    @Override
    public void createProviderTerminal(String merchantSn, Integer provider) {
        doCreateProviderTerminal(merchantSn, provider);
    }

    /**
     * 创建终端绑定
     *
     * @param merchantSn
     * @param provider
     */
    @Override
    public void doCreateProviderTerminal(String merchantSn, Integer provider) {
        handleSqbMerchantProviderTerminal(merchantSn, provider);
        //商户下门店
        Map merchant = merchantService.getMerchantBySn(merchantSn);
        if (WosaiMapUtils.isEmpty(merchant)) {
            log.info("商户号:{}商户不存在", merchantSn);
            return;
        }
        String merchantId = WosaiMapUtils.getString(merchant, DaoConstants.ID);
        final List<Map> storeList = storeBiz.getStoreListByMerchantId(merchantId);
        //绑定门店级别终端
        if (!CollectionUtils.isEmpty(storeList)) {
            log.info("商户号:{}开始绑定门店,门店数据:{}", merchantSn, JSONObject.toJSONString(storeList));
            storeList.stream().forEach(store -> handleSqbStoreTerminal(BeanUtil.getPropString(store, Store.SN), merchantSn, provider));
        }
        //商户下所有绑定的终端
        Map filter = CollectionUtil.hashMap(Terminal.STATUS, Terminal.STATUS_ACTIVATED);
        ListResult terminals = terminalService.getTerminals(merchantId, null, null, filter);

        List<String> terminalLevel = Lists.newArrayList(applicationApolloConfig.getTerminalLevelVendorAppAppid());
        final List<Map> terminalList = Optional.ofNullable(terminals.getRecords()).orElseGet(ArrayList::new).parallelStream()
                .filter(ter -> terminalLevel.contains(BeanUtil.getPropString(ter, Terminal.VENDOR_APP_APPID)))
                .filter(Objects::nonNull).collect(Collectors.toList());
        //终端绑定
        if (!CollectionUtils.isEmpty(terminalList)) {
            log.info("商户号:{}开始绑定终端,终端数据:{}", merchantSn, JSONObject.toJSONString(terminalList));
            terminalList.stream().forEach(terminal -> handleSqbTerminalBind(new MyObjectMapper().convertValue(merchant, MerchantInfo.class), terminal, provider));
        }
    }


    @Override
    public void handleSqbTerminalBind(MerchantInfo merchant, Map terminal, Integer provider) {
        final String vendorAppAppid = BeanUtil.getPropString(terminal, Terminal.VENDOR_APP_APPID);
        final String storeId = BeanUtil.getPropString(terminal, Terminal.STORE_ID);
        StoreInfo store = storeService.getStoreById(storeId, null);
        final String storeSn = store.getSn();
        final String merchantSn = merchant.getSn();
        final String terminalSn = BeanUtil.getPropString(terminal, Terminal.SN);
        //该通道下所有间连子商户号
        List<MerchantProviderParams> params = getMerchantProviderParamsByProvider(provider, merchantSn);
        if (CollectionUtils.isEmpty(params)) {
            return;
        }
        MerchantProviderParams acquirerParams = getAcquirerParams(merchantSn);
        //provider_terminal 不存在该门店的终端记录
        final ProviderTerminal providerTerminal = providerTerminalBiz.existProviderTerminal(provider, storeSn, merchantSn, terminalSn, acquirerParams.getPay_merchant_id());
        if (Objects.isNull(providerTerminal)) {
            final String providerTerminalId = providerTerminalIdBiz.getProviderTerminalIdBySqbTerminal();
            try {
                sqbTerminalConnectionProviderTerminal(merchantSn, providerTerminalId, provider, vendorAppAppid, terminalSn, storeSn);
                log.info("终端绑定=>新创建终端Id:{},门店:{},交易参数:{},provider:{}", providerTerminalId, storeSn, JSONObject.toJSONString(params), provider);
            } catch (Exception e) {
                log.error("终端绑定失败=>新创建终端Id:{},门店:{},交易参数:{},provider:{}", providerTerminalId, storeSn, JSONObject.toJSONString(params), provider, e);
                return;
            }
            params.stream().forEach(param ->
                    providerTerminalTaskRepository.addBoundTerminalTask(merchantSn,
                            param.getPay_merchant_id(),
                            provider,
                            param.getPayway(),
                            ProviderTerminalTaskTypeEnum.BOUND_ALL_SUB_MCH.getType(),
                            providerTerminalId,
                            storeSn,
                            terminalSn));
            return;
        }
        //判断是否需要插入绑定任务
        final List<MerchantProviderParams> merchantProviderParams = providerTerminalBiz.getNotSyncMerchantProviderParams(providerTerminal, params);
        //已经绑定了不用处理
        if (CollectionUtils.isEmpty(merchantProviderParams)) {
            return;
        }
        log.info("终端绑定=>已经存在收钱吧终端Id:{},门店:{},交易参数:{}", providerTerminal.getProvider_terminal_id(), storeSn, JSONObject.toJSONString(merchantProviderParams));
        //未绑定的子商户号插入task
        merchantProviderParams.stream().forEach(param ->
                providerTerminalTaskRepository.addBoundTerminalTask(merchantSn,
                        param.getPay_merchant_id(),
                        provider,
                        param.getPayway(),
                        ProviderTerminalTaskTypeEnum.BOUND_ALL_SUB_MCH.getType(),
                        providerTerminal.getProvider_terminal_id(),
                        storeSn,
                        terminalSn));
    }

    @Override
    public void handleSqbTerminalUnBind(MerchantInfo merchant, Map terminal, Integer provider) {
        final String storeId = BeanUtil.getPropString(terminal, Terminal.STORE_ID);
        StoreInfo store = storeService.getStoreById(storeId, null);
        final String storeSn = store.getSn();
        final String merchantSn = merchant.getSn();
        final String terminalSn = BeanUtil.getPropString(terminal, Terminal.SN);
        //provider_terminal 不存在该门店的终端记录
        MerchantProviderParams acquirerParams = getAcquirerParams(merchantSn);
        final ProviderTerminal providerTerminal = providerTerminalBiz.existProviderTerminal(provider, storeSn, merchantSn, terminalSn, acquirerParams.getPay_merchant_id());
        //无需解绑
        if (Objects.isNull(providerTerminal)) {
            return;
        }
        //该通道下所有间连子商户号
        List<MerchantProviderParams> params = getMerchantProviderParamsByProvider(provider, merchantSn);
        //判断是否需要插入解绑任务
        final List<MerchantProviderParams> needDeleteMerchantProviderParams = providerTerminalBiz.getNeedDeleteMerchantProviderParams(providerTerminal, params);

        needDeleteMerchantProviderParams.stream().forEach(
                param -> providerTerminalTaskRepository.addBoundTerminalTask(merchantSn,
                        param.getPay_merchant_id(),
                        provider, param.getPayway(),
                        ProviderTerminalTaskTypeEnum.UNBIND_TERMINAL_ALL_SUB_MCH.getType(),
                        providerTerminal.getProvider_terminal_id(),
                        providerTerminal.getStore_sn(),
                        terminalSn)
        );
    }


    @Override
    public void handleSqbStoreTerminal(String storeSn, String merchantSn, Integer provider) {
        MerchantProviderParams acquirerParams = getAcquirerParams(merchantSn);
        //provider_terminal 是否存在该门店的终端记录
        final ProviderTerminal providerTerminal = providerTerminalBiz.existStoreProviderTerminal(provider, storeSn, merchantSn, acquirerParams.getPay_merchant_id());
        //该通道下所有间连子商户号
        List<MerchantProviderParams> params = getMerchantProviderParamsByProvider(provider, merchantSn);
        if (CollectionUtils.isEmpty(params)) {
            return;
        }
        //不存在则绑定
        if (Objects.isNull(providerTerminal)) {
            final String providerTerminalId = providerTerminalIdBiz.getProviderTerminalIdBySqbStore();
            try {
                sqbStoreTerminalConnectionProviderTerminal(merchantSn, providerTerminalId, provider, storeSn);
                log.info("通联门店绑定=>新创建终端Id:{},门店:{},交易参数:{},provider:{}", providerTerminalId, storeSn, JSONObject.toJSONString(params), provider);
            } catch (Exception e) {
                log.info("通联门店绑定失败=>新创建终端Id:{},门店:{},交易参数:{},provider:{},", providerTerminalId, storeSn, JSONObject.toJSONString(params), provider, e);
                return;
            }
            params.stream().forEach(param ->
                    providerTerminalTaskRepository.addBoundTerminalTask(
                            merchantSn,
                            param.getPay_merchant_id(),
                            provider,
                            param.getPayway(),
                            ProviderTerminalTaskTypeEnum.BIND_STORE_ALL_SUB_MCH.getType(),
                            providerTerminalId,
                            storeSn,
                            null));
            return;
        }
        //判断是否需要插入绑定任务
        final List<MerchantProviderParams> merchantProviderParams = providerTerminalBiz.getNotSyncMerchantProviderParams(providerTerminal, params);
        //已经绑定了不用处理
        if (CollectionUtils.isEmpty(merchantProviderParams)) {
            return;
        }
        log.info("门店绑定=>已经存在终端Id:{},门店:{},交易参数:{}", providerTerminal.getProvider_terminal_id(), storeSn, JSONObject.toJSONString(merchantProviderParams));
        //未绑定的子商户号插入task
        merchantProviderParams.stream().forEach(param ->
                providerTerminalTaskRepository.addBoundTerminalTask(merchantSn,
                        param.getPay_merchant_id(),
                        provider, param.getPayway(),
                        ProviderTerminalTaskTypeEnum.BIND_STORE_ALL_SUB_MCH.getType(),
                        providerTerminal.getProvider_terminal_id(),
                        storeSn,
                        null)
        );
    }

    @Override
    public void handleSqbMerchantProviderTerminal(String merchantSn, Integer provider) {
        MerchantProviderParams acquirerParams = getAcquirerParams(merchantSn);
        final ProviderTerminal providerTerminal = providerTerminalBiz.existMerchantProviderTerminal(provider, merchantSn, acquirerParams.getPay_merchant_id());
        //该通道下所有间连子商户号
        List<MerchantProviderParams> params = getMerchantProviderParamsByProvider(provider, merchantSn);
        if (Objects.isNull(providerTerminal)) {
            //不存在则绑定
            final String termNo = providerTerminalIdBiz.getProviderTerminalIdByMerchantSn();
            try {
                merchantConnectionProviderTerminal(merchantSn, termNo, provider);
                log.info("通联商户绑定=>商户号:{},不存在商户级别终端,开始重新绑定,终端Id:{},交易参数:{},provider:{}", merchantSn, termNo, JSONObject.toJSONString(params), provider);
            } catch (Exception e) {
                log.error("通联商户绑定失败=>商户号:{},不存在商户级别终端,开始重新绑定,终端Id:{},交易参数:{},provider:{}", merchantSn, termNo, JSONObject.toJSONString(params), provider, e);
                return;
            }
            params.stream().forEach(param ->
                    providerTerminalTaskRepository.addBoundTerminalTask(merchantSn,
                            param.getPay_merchant_id(),
                            param.getProvider(),
                            param.getPayway(),
                            ProviderTerminalTaskTypeEnum.BOUND_ALL_TERMINAL.getType(),
                            termNo,
                            null,
                            null));
            return;
        }
        //判断是否需要插入绑定任务
        final List<MerchantProviderParams> merchantProviderParams = providerTerminalBiz.getNotSyncMerchantProviderParams(providerTerminal, params);
        //已经绑定了不用处理
        if (CollectionUtils.isEmpty(merchantProviderParams)) {
            return;
        }
        log.info("通联商户绑定=>商户号:{},已经存在商户级别终端,新增子商户号,终端Id:{},交易参数:{}", merchantSn, providerTerminal.getProvider_terminal_id(), JSONObject.toJSONString(merchantProviderParams));
        //未绑定的子商户号插入task
        merchantProviderParams.stream().forEach(param ->
                providerTerminalTaskRepository.addBoundTerminalTask(merchantSn,
                        param.getPay_merchant_id(),
                        param.getProvider(),
                        param.getPayway(),
                        ProviderTerminalTaskTypeEnum.BOUND_ALL_TERMINAL.getType(),
                        providerTerminal.getProvider_terminal_id(),
                        null,
                        null)
        );
    }


    @Override
    public ContractResponse updateTerminal(UpdateTermInfoDTO dto, int payWay, String terminalSn) {
        TongLianV2Param tongLianV2Param = contractParamsBiz.buildContractParamsByPayMchId(dto.getSubMchId(), TongLianV2Param.class);
        return tongLianV2Service.updateTerm(dto, tongLianV2Param);
    }

    @Override
    public List getFeeRate(String merchantId) {
        if (org.apache.commons.lang3.StringUtils.isEmpty(merchantId)) {
            return null;
        }
        List<Map> merchantConfigs = tradeConfigService.getAnalyzedMerchantConfigs(merchantId);
        Map wechatConfig = null;
        Map alipayConfig = null;
        Map unionConfig = null;
        for (Map merchantConfig : merchantConfigs) {
            int payway = BeanUtil.getPropInt(merchantConfig, MerchantConfig.PAYWAY);
            if (payway == PaywayEnum.ALIPAY.getValue()) {
                alipayConfig = merchantConfig;
            } else if (payway == PaywayEnum.WEIXIN.getValue()) {
                wechatConfig = merchantConfig;
            } else if (payway == PaywayEnum.UNIONPAY.getValue()) { //银联二维码支付
                unionConfig = merchantConfig;
            }
        }
        if (Objects.isNull(wechatConfig) || Objects.isNull(alipayConfig) || Objects.isNull(unionConfig)) {
            throw new ContextParamException("商户的支付宝或者微信交易配置merchant_config未配置");
        }

        List result = new ArrayList();
        Map aliMap = CollectionUtil.hashMap(
                PID_FIELD, PID_VALUE,
                MTRXCODE_FIELD, MTRXCODE_VALUE_ALIPAY
        );
        String aliFeeRate = null;
        String alipayLadder = getLadderFeeRate(alipayConfig);
        if (org.apache.commons.lang3.StringUtils.isNotEmpty(alipayLadder)) {
            aliMap.put(RANKFEEARR_FIELD, alipayLadder);
        } else {
            aliFeeRate = formatFeerate(MapUtils.getString(alipayConfig, MerchantConfig.WAP_FEE_RATE));
            aliMap.put(FEERATE_FIELD, aliFeeRate);
        }

        if (WosaiStringUtils.isEmpty(aliFeeRate) || Double.valueOf(aliFeeRate) > 0) {
            result.add(aliMap);
        }


        Map wechatMap = CollectionUtil.hashMap(
                PID_FIELD, PID_VALUE,
                MTRXCODE_FIELD, MTRXCODE_VALUE_WECHAT
        );
        String wxFeeRate = null;
        String wechatLadder = getLadderFeeRate(wechatConfig);
        if (org.apache.commons.lang3.StringUtils.isNotEmpty(wechatLadder)) {
            wechatMap.put(RANKFEEARR_FIELD, wechatLadder);
        } else {
            wxFeeRate = formatFeerate(MapUtils.getString(wechatConfig, MerchantConfig.WAP_FEE_RATE));
            wechatMap.put(FEERATE_FIELD, wxFeeRate);
        }
        if (WosaiStringUtils.isEmpty(wxFeeRate) || Double.valueOf(wxFeeRate) > 0) {
            result.add(wechatMap);
        }


        String unionFeeRate = null;
        Map unionMap = CollectionUtil.hashMap(
                PID_FIELD, PID_VALUE,
                MTRXCODE_FIELD, MTRXCODE_VALUE_UNIONPAY
        );
        String unionLadder = getUnionLadderFeeRate(unionConfig);
        if (org.apache.commons.lang3.StringUtils.isNotEmpty(unionLadder)) {
            unionMap.put(RANKFEEARR_FIELD, unionLadder);
        } else {
            unionFeeRate = formatFeerate(MapUtils.getString(unionConfig, MerchantConfig.WAP_FEE_RATE));
            unionMap.put(CREDITFEERATE_FIELD, unionFeeRate);
            unionMap.put(FEERATE_FIELD, unionFeeRate);
        }
        if (WosaiStringUtils.isEmpty(unionFeeRate) || Double.valueOf(unionFeeRate) > 0) {
            result.add(unionMap);
        }
        return result;
    }

    @Override
    protected void merchantConnectionProviderTerminal(String merchantSn, String termNo, Integer provider) {
        MerchantProviderParams acquirerParams = getAcquirerParams(merchantSn);
        AddTermInfoDTO addTermInfoDTO = new AddTermInfoDTO();
        addTermInfoDTO.setDeviceId(termNo);
        addTermInfoDTO.setSubMchId(acquirerParams.getPay_merchant_id());
        addTermInfoDTO.setMerchantSn(merchantSn);
        TongLianV2Param tongLianV2Param = contractParamsBiz.buildContractParamsByParams(acquirerParams, TongLianV2Param.class);
        ContractResponse response = tongLianV2Service.addTerm(addTermInfoDTO, tongLianV2Param);
        if (!response.isSuccess()) {
            throw new ContractBizException("通联收银宝终端采集信息失败:" + merchantSn + ":" + response.getMessage());
        }
        providerTerminalBiz.merchantConnectionProviderTerminal(merchantSn, termNo, acquirerParams.getPay_merchant_id(), provider);
    }

    @Override
    protected void sqbStoreTerminalConnectionProviderTerminal(String merchantSn, String providerTerminalId, Integer provider, String storeSn) {
        MerchantProviderParams acquirerParams = getAcquirerParams(merchantSn);
        AddTermInfoDTO addTermInfoDTO = new AddTermInfoDTO();
        addTermInfoDTO.setDeviceId(providerTerminalId);
        addTermInfoDTO.setSubMchId(acquirerParams.getPay_merchant_id());
        addTermInfoDTO.setMerchantSn(merchantSn);

        TongLianV2Param tongLianV2Param = contractParamsBiz.buildContractParamsByParams(acquirerParams, TongLianV2Param.class);
        ContractResponse response = tongLianV2Service.addTerm(addTermInfoDTO, tongLianV2Param);
        if (!response.isSuccess()) {
            throw new ContractBizException("通联收银宝终端采集信息失败:" + merchantSn + ":" + response.getMessage());
        }
        providerTerminalBiz.sqbStoreTerminalConnectionProviderTerminal(merchantSn, providerTerminalId, acquirerParams.getPay_merchant_id(), provider, storeSn);
    }

    @Override
    protected void sqbTerminalConnectionProviderTerminal(String merchantSn, String providerTerminalId, Integer provider, String vendorAppAppid, String terminalSn, String storeSn) {
        MerchantProviderParams acquirerParams = getAcquirerParams(merchantSn);
        AddTermInfoDTO addTermInfoDTO = new AddTermInfoDTO();
        addTermInfoDTO.setDeviceId(providerTerminalId);
        addTermInfoDTO.setSubMchId(acquirerParams.getPay_merchant_id());
        addTermInfoDTO.setMerchantSn(merchantSn);

        TongLianV2Param tongLianV2Param = contractParamsBiz.buildContractParamsByParams(acquirerParams, TongLianV2Param.class);
        ContractResponse response = tongLianV2Service.addTerm(addTermInfoDTO, tongLianV2Param);
        if (!response.isSuccess()) {
            throw new ContractBizException("通联收银宝终端采集信息失败:" + merchantSn + ":" + response.getMessage());
        }
        providerTerminalBiz.sqbTerminalConnectionProviderTerminal(merchantSn, providerTerminalId, acquirerParams.getPay_merchant_id(), provider, vendorAppAppid, terminalSn, storeSn);
    }

    @Override
    protected String formatFeerate(String feerate) {
        // 防止精度问题 Double.valueOf("0.39) * 10 = 3.9000000000000004
        return new DecimalFormat("#.#").format(Double.parseDouble(feerate) * 10);
    }

    private String getLadderFeeRate(Map feeRateConfig) {
        if (!isLadderFeeRate(feeRateConfig)) {
            return null;
        }
        List<ListMchFeeRateResult.LadderFeeRate> ladderFeeRates = JSONObject.parseArray(JSONObject.toJSONString(BeanUtil.getProperty(feeRateConfig, MerchantConfig.LADDER_FEE_RATES)), ListMchFeeRateResult.LadderFeeRate.class);
        ladderFeeRates.sort(Comparator.comparing(ListMchFeeRateResult.LadderFeeRate::getMax, Comparator.nullsLast(Double::compareTo)));
        List<Map> collect = ladderFeeRates.stream().map(ladderFeeRate ->
                CollectionUtil.hashMap(
                        RANK_FIELD, getLadderCell(ladderFeeRate.getMin()) + "-" + getLadderCell(ladderFeeRate.getMax()),
                        FEERATE_FIELD, formatFeerate(ladderFeeRate.getWapFeeRate())
                )
        ).collect(Collectors.toList());
        return JSON.toJSONString(collect);
    }

    /**
     * 在信用卡费率暂未开发成功时，为了能通过通联云闪付的特殊操作
     *
     * @param feeRateConfig
     * @return
     */
    private String getUnionLadderFeeRate(Map feeRateConfig) {
        if (!isLadderFeeRate(feeRateConfig)) {
            return null;
        }
        List<ListMchFeeRateResult.LadderFeeRate> ladderFeeRates = JSONObject.parseArray(JSONObject.toJSONString(BeanUtil.getProperty(feeRateConfig, MerchantConfig.LADDER_FEE_RATES)), ListMchFeeRateResult.LadderFeeRate.class);
        ladderFeeRates.sort(Comparator.comparing(ListMchFeeRateResult.LadderFeeRate::getMax, Comparator.nullsLast(Double::compareTo)));
        List<Map> collect = ladderFeeRates.stream().map(ladderFeeRate ->
                CollectionUtil.hashMap(
                        RANK_FIELD, getLadderCell(ladderFeeRate.getMin()) + "-" + getLadderCell(ladderFeeRate.getMax()),
                        FEERATE_FIELD, formatFeerate(ladderFeeRate.getWapFeeRate()),
                        CREDITFEERATE_FIELD, formatFeerate(ladderFeeRate.getWapFeeRate())
                )
        ).collect(Collectors.toList());
        return JSON.toJSONString(collect);
    }

    private String getLadderCell(Double fee) {
        if (Objects.isNull(fee)) {
            return "";
        }
        return String.valueOf(fee);
    }

    public String getMerchantName(String merchantSn) {
        MerchantProviderParams acquirerParams = getAcquirerParams(merchantSn);
        TongLianV2Param tongLianV2Param = contractParamsBiz.buildContractParamsByParams(acquirerParams, TongLianV2Param.class);
        ContractResponse response = tongLianV2Service.queryMerchantInfo(merchantSn, tongLianV2Param);
        if (!response.isSuccess()) {
            throw new ContractBizException("通联收银宝信息查询失败:" + merchantSn + ":" + response.getMessage());
        }
        Map<String, Object> result = response.getResponseParam();
        String comproperty = MapUtils.getString(result, "comproperty");
        MerchantType merchantType = MerchantType.valueFromTlStatus(comproperty);
        if (Objects.isNull(merchantType)) {
            throw new ContractBizException("通联收银宝商户类型异常:" + merchantSn + ":" + comproperty);
        }
        if (MerchantType.TYPE_MICRO.equals(merchantType)) {
            return "商户_" + MapUtils.getString(result, "acctname");
        }
        return MapUtils.getString(result, "corpbusname");
    }

    @Override
    public Map doWeixinSubDevConfig(WeixinConfig weixinConfig, MerchantProviderParams merchantProviderParams) {
        TongLianV2Param tongLianV2Param = contractParamsBiz.buildContractParamsByParams(merchantProviderParams, TongLianV2Param.class);
        ContractResponse response = tongLianV2Service.weixinSubdevConfig(weixinConfig, tongLianV2Param);
        if (response.isSuccess()) {
            return CollectionUtil.hashMap("result_code", "1", "message", "配置成功");
        } else {
            return CollectionUtil.hashMap("result_code", "0", "message", response.getMessage());
        }
    }


    private MerchantProviderParams getAcquirerParams(String merchantSn) {
        MerchantProviderParamsExample dto = new MerchantProviderParamsExample();
        dto.createCriteria().andMerchant_snEqualTo(merchantSn).andPaywayEqualTo(PaywayEnum.ACQUIRER.getValue()).andProviderEqualTo(ProviderEnum.PROVIDER_TONGLIAN_V2.getValue()).andDeletedEqualTo(false);
        return merchantProviderParamsMapper.selectByExample(dto).get(0);
    }

}