package com.wosai.upay.job.refactor.model.bo;

import com.wosai.upay.job.refactor.model.enums.InternalScheduleSubTaskStatusEnum;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Objects;

/**
 * 内部调度子任务处理结果通用返回BO
 *
 * <AUTHOR>
 * @date 2024/6/17 15:40
 */
@Data
@NoArgsConstructor
public class InternalScheduleSubTaskProcessResultBO {

    public InternalScheduleSubTaskProcessResultBO(InternalScheduleSubTaskStatusEnum status,String result) {
        this.status = status;
        this.result = result;
    }

    /**
     * 执行结果状态
     */
    private InternalScheduleSubTaskStatusEnum status;

    /**
     * 请求报文
     */
    private String requestMsg;

    /**
     * 响应报文
     */
    private String responseMsg;

    /**
     * 处理结果
     */
    private String result;

    /**
     * 当前正在使用的收单机构生成的进件任务ID
     */
    private Long contracrTaskId;




    public static InternalScheduleSubTaskProcessResultBO fail(String result) {
        return new InternalScheduleSubTaskProcessResultBO(InternalScheduleSubTaskStatusEnum.PROCESS_FAIL, result);
    }

    public static InternalScheduleSubTaskProcessResultBO success(String result) {
        return new InternalScheduleSubTaskProcessResultBO(InternalScheduleSubTaskStatusEnum.PROCESS_SUCCESS, result);
    }

    public static InternalScheduleSubTaskProcessResultBO waitExternalResult(String result) {
        return new InternalScheduleSubTaskProcessResultBO(InternalScheduleSubTaskStatusEnum.WAIT_EXTERNAL_RESULT, result);
    }

    public void updateStatusFail(String result) {
        this.status = InternalScheduleSubTaskStatusEnum.PROCESS_FAIL;
        this.result = result;
    }

    public void updateStatusSuccess(String result) {
        this.status = InternalScheduleSubTaskStatusEnum.PROCESS_SUCCESS;
        this.result = result;
    }

    public void updateStatusWaitExternalResult(String result) {
        this.status = InternalScheduleSubTaskStatusEnum.WAIT_EXTERNAL_RESULT;
        this.result = result;
    }

    public void updateStatusWaiting(String result) {
        this.status = InternalScheduleSubTaskStatusEnum.WAIT_PROCESS;
        this.result = result;
    }

    public boolean processStatusIsFail() {
        return Objects.equals(InternalScheduleSubTaskStatusEnum.PROCESS_FAIL, status);
    }

    public boolean processStatusIsSuccess() {
        return Objects.equals(InternalScheduleSubTaskStatusEnum.PROCESS_SUCCESS, status);
    }

}
