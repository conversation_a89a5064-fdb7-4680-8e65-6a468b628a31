package com.wosai.upay.job.xxljob.batch;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.google.common.util.concurrent.RateLimiter;
import com.shouqianba.cua.enums.contract.ProviderEnum;
import com.shouqianba.cua.enums.core.PaywayEnum;
import com.wosai.common.utils.WosaiCollectionUtils;
import com.wosai.data.util.CollectionUtil;
import com.wosai.upay.job.biz.AgentAppidBiz;
import com.wosai.upay.job.biz.ContractParamsBiz;
import com.wosai.upay.job.biz.RuleContext;
import com.wosai.upay.job.enume.ChannelEnum;
import com.wosai.upay.job.mapper.MerchantProviderParamsMapper;
import com.wosai.upay.job.model.CommonModel;
import com.wosai.upay.job.model.ContractChannel;
import com.wosai.upay.job.model.DO.MerchantProviderParams;
import com.wosai.upay.job.model.dto.WeixinSubAppidDto;
import com.wosai.upay.job.util.ChatBotUtil;
import com.wosai.upay.job.util.CommonUtil;
import com.wosai.upay.job.util.ExceptionUtil;
import com.wosai.upay.job.xxljob.model.BatchJobParam;
import com.wosai.upay.job.xxljob.template.AbstractBatchJobHandler;
import com.wosai.upay.merchant.contract.model.ContractResponse;
import com.wosai.upay.merchant.contract.model.WeixinAppidConfig;
import com.wosai.upay.merchant.contract.model.WeixinConfig;
import com.wosai.upay.merchant.contract.model.provider.*;
import com.wosai.upay.merchant.contract.service.*;
import org.apache.commons.collections.MapUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.BeanPropertyRowMapper;
import org.springframework.jdbc.core.namedparam.NamedParameterJdbcTemplate;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;


/**
 * 配置微信公众号
 * 微信通道进件完成后，需要配置支付目录、交易公众号、关注公众号
 * Created by lihebin on 2018/6/3.
 */
@Component("WeixinSubdevConfigJobHandler")
public class WeixinSubdevConfigJobHandler extends AbstractBatchJobHandler<MerchantProviderParams> {
    private final static Logger log = LoggerFactory.getLogger(WeixinSubdevConfigJobHandler.class);

    @Autowired
    private UnionService unionService;

    @Autowired
    private ChatBotUtil chatBotUtil;

    @Autowired
    private AgentAppidBiz agentAppidBiz;

    @Autowired
    private TongLianService tongLianService;

    @Autowired
    ContractParamsBiz contractParamsBiz;

    @Autowired
    private MerchantProviderParamsMapper merchantProviderParamsMapper;

    @Resource(name = "merchantContractNPJdbcTemplate")
    private NamedParameterJdbcTemplate merchantContractNPJdbcTemplate;

    @Autowired
    private ChinaUmsService chinaUmsService;

    @Autowired
    private PsbcService psbcService;
    @Autowired
    private GuangFaService guangFaService;

    @Autowired
    private CcbService ccbService;
    @Autowired
    private HXService hxService;

    @Autowired
    private HaikeService haikeService;

    @Autowired
    private FuyouService fuyouService;
    @Autowired
    private GuotongService guotongService;


    @Autowired
    private TongLianV2Service tongLianV2Service;

    @Autowired
    private UmbService umbService;

    @Autowired
    private RuleContext ruleContext;


    private static RateLimiter rateLimiter = RateLimiter.create(1.5);

    @Override
    public List<MerchantProviderParams> queryTaskItems(BatchJobParam param) {
        return listWeixinSubV2ProviderParams(System.currentTimeMillis() - param.getQueryTime(), param.getBatchSize());
    }

    @Override
    public String getLockKey(MerchantProviderParams merchantProviderParams) {
        return "WeixinSubdevConfigJobHandler:" + merchantProviderParams.getId();
    }

    /**
     * 微信公众号配置2.0
     */
    @Override
    public void doHandleSingleData(MerchantProviderParams merchantProviderParams) {
        MerchantProviderParams merchantProviderParam = merchantProviderParamsMapper.selectByPrimaryKey(merchantProviderParams.getId());
        if (MerchantProviderParams.PARAMS_CONFIG_STATUS_SUCCESS == merchantProviderParam.getParams_config_status() ||
                MerchantProviderParams.PARAMS_CONFIG_STATUS_NULL == merchantProviderParam.getParams_config_status()) {
            return;
        }
        int provider = merchantProviderParam.getProvider();
        if (provider == merchantProviderParam.getPayway()) {
            return;
        }
        if (!rateLimiter.tryAcquire(10, TimeUnit.SECONDS)) {
            return;
        }
        Map result = CollectionUtil.hashMap();
        WeixinConfig configs = null;
        MerchantProviderParams updateParam = new MerchantProviderParams()
                .setId(merchantProviderParams.getId())
                .setMtime(System.currentTimeMillis());
        Map extra = CommonUtil.bytes2Map(merchantProviderParam.getExtra());
        try {
            if (ProviderEnum.PROVIDER_TONGLIAN.getValue() == provider) {
                TongLianParam tongLianParam = contractParamsBiz.buildContractParams(merchantProviderParam.getProvider() + "", merchantProviderParam.getPayway(), merchantProviderParam.getChannel_no(), TongLianParam.class);
                configs = agentAppidBiz.getConfig(merchantProviderParam.getMerchant_sn(), merchantProviderParam.getProvider(), tongLianParam.getPayway_channel_no(), merchantProviderParam.getPay_merchant_id());
                result = tongLianService.weixinSubdevConfigV2(tongLianParam, configs);
            } else if (ProviderEnum.PROVIDER_TONGLIAN_V2.getValue() == provider) {
                TongLianV2Param tongLianV2Param = contractParamsBiz.buildContractParams(merchantProviderParam.getProvider() + "", merchantProviderParam.getPayway(), merchantProviderParam.getChannel_no(), TongLianV2Param.class);
                configs = agentAppidBiz.getConfig(merchantProviderParam.getMerchant_sn(), merchantProviderParam.getProvider(), tongLianV2Param.getPayway_channel_no(), merchantProviderParam.getPay_merchant_id());
                ContractResponse response = tongLianV2Service.weixinSubdevConfig(configs, tongLianV2Param);
                if (response.isSuccess()) {
                    result = CollectionUtil.hashMap("result_code", "1", "message", "配置成功");
                } else {
                    result = CollectionUtil.hashMap("result_code", "0", "message", response.getMessage());
                }
            } else if (ProviderEnum.PROVIDER_DIRECT_UNIONPAY.getValue() == provider || ProviderEnum.PROVIDER_LKLWANMA.getValue() == provider || ProviderEnum.PROVIDER_UNIONPAY.getValue() == provider || ProviderEnum.PROVIDER_LKLORG.getValue() == provider) {
                UnionWeixinParam unionWeixinParam = contractParamsBiz.buildContractParams
                        (merchantProviderParam.getProvider() + "", merchantProviderParam.getPayway(), merchantProviderParam.getChannel_no(), UnionWeixinParam.class);
                configs = agentAppidBiz.getConfig(merchantProviderParam.getMerchant_sn(), merchantProviderParam.getProvider().equals(ProviderEnum.PROVIDER_LKLORG.getValue()) ? ProviderEnum.PROVIDER_LKLORG.getValue() : ProviderEnum.PROVIDER_DIRECT_UNIONPAY.getValue(), merchantProviderParam.getChannel_no(), merchantProviderParam.getPay_merchant_id());
                if (configs == null || WosaiCollectionUtils.isEmpty(configs.getAppidConfigs())) {
                    updateParam.setParams_config_status(2);
                    merchantProviderParamsMapper.updateByPrimaryKeySelective(updateParam);
                    return;
                }
                result = unionService.weixinSubdevConfigV2(unionWeixinParam, configs);
            } else if (ProviderEnum.PROVIDER_UMS.getValue() == provider) {
                ChinaUmsParam umsParam = contractParamsBiz.buildContractParams(String.valueOf(merchantProviderParam.getProvider()), merchantProviderParam.getPayway(), merchantProviderParam.getChannel_no(), ChinaUmsParam.class);
                configs = agentAppidBiz.getConfig(merchantProviderParam.getMerchant_sn(), merchantProviderParam.getProvider(), umsParam.getChannel_no(), merchantProviderParam.getPay_merchant_id());
                result = chinaUmsService.wechatSubDevConfig(umsParam, configs);
            } else if (ProviderEnum.PROVIDER_PSBC.getValue() == provider) {
                PsbcParam psbcParam = contractParamsBiz.buildContractParams(String.valueOf(merchantProviderParam.getProvider()), merchantProviderParam.getPayway(), merchantProviderParam.getChannel_no(), PsbcParam.class);
                configs = agentAppidBiz.getConfig(merchantProviderParam.getMerchant_sn(), ProviderEnum.PROVIDER_PSBC.getValue(), merchantProviderParam.getChannel_no(), merchantProviderParam.getPay_merchant_id());
                result = psbcService.weChatSubDevConfig(psbcParam, configs);
            } else if (ProviderEnum.PROVIDER_CGB.getValue() == provider) {
                GuangFaParam guangFaParam = contractParamsBiz.buildContractParams(String.valueOf(merchantProviderParam.getProvider()), merchantProviderParam.getPayway(), merchantProviderParam.getChannel_no(), GuangFaParam.class);
                configs = agentAppidBiz.getConfig(merchantProviderParam.getMerchant_sn(), merchantProviderParam.getProvider(), guangFaParam.getChannel_no(), merchantProviderParam.getPay_merchant_id());
                result = guangFaService.wechatSubDevConfig(guangFaParam, configs);
            } else if (ProviderEnum.PROVIDER_CCB.getValue() == provider) {
                CcbParam ccbParam = contractParamsBiz.buildContractParams(String.valueOf(merchantProviderParam.getProvider()), merchantProviderParam.getPayway(), merchantProviderParam.getChannel_no(), CcbParam.class);
                configs = agentAppidBiz.getConfig(merchantProviderParam.getMerchant_sn(), merchantProviderParam.getProvider(), merchantProviderParam.getChannel_no(), merchantProviderParam.getPay_merchant_id());
                result = ccbService.weChatSubDevConfig(ccbParam, configs);
            } else if (ProviderEnum.PROVIDER_HXB.getValue() == provider) {
                // TODO 配置微信交易参数
                HXParam hxParam = contractParamsBiz.buildContractParams(String.valueOf(merchantProviderParam.getProvider()), merchantProviderParam.getPayway(), merchantProviderParam.getChannel_no(), HXParam.class);
                configs = agentAppidBiz.getConfig(merchantProviderParam.getMerchant_sn(), merchantProviderParam.getProvider(), merchantProviderParam.getChannel_no(), merchantProviderParam.getPay_merchant_id());
                result = hxService.wechatSubDevConfig(configs, hxParam);
            } else if (ProviderEnum.PROVIDER_HAIKE.getValue() == provider) {
                configs = agentAppidBiz.getConfig(merchantProviderParam.getMerchant_sn(), merchantProviderParam.getProvider(), merchantProviderParam.getChannel_no(), merchantProviderParam.getPay_merchant_id());
                if (configs == null || WosaiCollectionUtils.isEmpty(configs.getAppidConfigs())) {
                    updateParam.setParams_config_status(MerchantProviderParams.PARAMS_CONFIG_STATUS_NULL);
                    merchantProviderParamsMapper.updateByPrimaryKeySelective(updateParam);
                    return;
                }
                HaikeParam haikeParam = contractParamsBiz.buildContractParams(ChannelEnum.HAIKE.getValue(), HaikeParam.class);
                result = haikeService.weixinSubdevConfig(configs, haikeParam);
            } else if (ProviderEnum.PROVIDER_FUYOU.getValue() == provider) {
                configs = agentAppidBiz.getConfig(merchantProviderParam.getMerchant_sn(), merchantProviderParam.getProvider(), merchantProviderParam.getChannel_no(), merchantProviderParam.getPay_merchant_id());
                if (configs == null || WosaiCollectionUtils.isEmpty(configs.getAppidConfigs())) {
                    updateParam.setParams_config_status(MerchantProviderParams.PARAMS_CONFIG_STATUS_NULL);
                    merchantProviderParamsMapper.updateByPrimaryKeySelective(updateParam);
                    return;
                }
                FuyouParam fuyouParam = contractParamsBiz.buildContractParams(ChannelEnum.FUYOU.getValue(), FuyouParam.class);
                result = fuyouService.weixinSubdevConfig(configs, fuyouParam);
            } else if (ProviderEnum.PROVIDER_GUOTONG.getValue() == provider) {
                configs = agentAppidBiz.getConfig(merchantProviderParam.getMerchant_sn(), merchantProviderParam.getProvider(), merchantProviderParam.getChannel_no(), merchantProviderParam.getPay_merchant_id());
                if (configs == null || WosaiCollectionUtils.isEmpty(configs.getAppidConfigs())) {
                    updateParam.setParams_config_status(MerchantProviderParams.PARAMS_CONFIG_STATUS_NULL);
                    merchantProviderParamsMapper.updateByPrimaryKeySelective(updateParam);
                    return;
                }
                GuotongParam guotongParam = contractParamsBiz.buildContractParams(ChannelEnum.GUOTONG.getValue(), GuotongParam.class);
                result = guotongService.wechatSubDevConfig(configs, guotongParam);
            } else if (ProviderEnum.PROVIDER_UMB.getValue() == provider) {
                configs = agentAppidBiz.getConfig(merchantProviderParam.getMerchant_sn(), merchantProviderParam.getProvider(), merchantProviderParam.getChannel_no(), merchantProviderParam.getPay_merchant_id());
                if (configs == null || WosaiCollectionUtils.isEmpty(configs.getAppidConfigs())) {
                    updateParam.setParams_config_status(MerchantProviderParams.PARAMS_CONFIG_STATUS_NULL);
                    merchantProviderParamsMapper.updateByPrimaryKeySelective(updateParam);
                    return;
                }
                ContractChannel contractChannel = ruleContext.getContractRule(merchantProviderParam.getContract_rule()).getContractChannel();
                String mchid = (String) contractChannel.getAcquirer_metadata().get("mchid");
                UMBParam umbParam = contractParamsBiz.buildParam(contractChannel, null, UMBParam.class);
                result = umbService.wechatSubDevConfig(configs, mchid, umbParam, merchantProviderParam.getProvider_merchant_id());
            }
            if (CommonModel.RESULT_CODE_SUCCESS.equals(MapUtils.getString(result, CommonModel.RESULT_CODE))) {

                List list = (List) extra.getOrDefault("appid_config_list", new ArrayList<>());
                final List<WeixinSubAppidDto> collect = (List<WeixinSubAppidDto>) list.stream().map(x -> JSONObject.parseObject(JSONArray.toJSONString(x), WeixinSubAppidDto.class)).collect(Collectors.toList());
                //将list转化为key为subAppId,value为WeixinSubAppidDto对象
                final Map<String, WeixinSubAppidDto> appidDtoMap = collect.stream().collect(Collectors.toMap(item -> item.getSub_appid(), item -> item, (val1, val2) -> val1));

                String weixinAppId = null, weixinMiniAppid = null, weixinSubscribeAppId = null, weixinReceiptAppid = null;
                for (WeixinAppidConfig config : configs.getAppidConfigs()) {
                    WeixinSubAppidDto appidDto = new WeixinSubAppidDto()
                            .setSub_appid(config.getSub_appid());
                    if (config.isMini()) {
                        weixinMiniAppid = config.getSub_appid();
                        weixinReceiptAppid = config.getReceipt_appid();
                        appidDto.setType(2);
                    } else {
                        weixinAppId = config.getSub_appid();
                        weixinReceiptAppid = config.getReceipt_appid();
                        weixinSubscribeAppId = config.getSubscribe_appid();
                        appidDto.setType(1);
                    }
                    appidDtoMap.put(config.getSub_appid(), appidDto);
                }
                updateParam.setWeixin_sub_appid(weixinAppId);
                updateParam.setWeixin_subscribe_appid(weixinSubscribeAppId);
                updateParam.setWeixin_sub_mini_appid(weixinMiniAppid);
                updateParam.setWeixin_receipt_appid(weixinReceiptAppid);
                updateParam.setParams_config_status(MerchantProviderParams.PARAMS_CONFIG_STATUS_SUCCESS);
                final Collection<WeixinSubAppidDto> weixinSubAppidDtos = appidDtoMap.values();
                extra.put("appid_config_list", weixinSubAppidDtos);
                updateParam.setExtra(CommonUtil.map2Bytes(extra));
                log.info("weixinSubdevConfig success: {}, configs: {}, result: {}", updateParam.getId(), configs, result);
            } else {
                updateParam.setParams_config_status(MerchantProviderParams.PARAMS_CONFIG_STATUS_FAIL);
                updateParam.setVersion(merchantProviderParam.getVersion() + 1);
                extra.put("weixin_subdev_config", result);
                updateParam.setExtra(CommonUtil.map2Bytes(extra));
                log.warn("weixinSubdevConfig fail: {}, configs:{}, result:{}", updateParam.getId(), configs, result);
            }
            merchantProviderParamsMapper.updateByPrimaryKeySelective(updateParam);
        } catch (Exception e) {
            chatBotUtil.sendMessageToContractWarnChatBot(merchantProviderParams.getId() + " 微信子商户号配置异常" + ExceptionUtil.getThrowableMsg(e));
            log.error("配置微信公众号异常,merchantProviderParamsId={}, WeixinConfig={}", updateParam.getId(), configs, e);
        }
    }

    public List<MerchantProviderParams> listWeixinSubV2ProviderParams(long startTime, int limit) {
        String sql = "select * from merchant_provider_params where payway =:payway and mtime >:mtime and params_config_status in (:params_config_status) and provider!=:payway and version < 100" +
                " order by mtime desc limit :limit";
        Map params = CollectionUtil.hashMap(CommonModel.PAYWAY, PaywayEnum.WEIXIN.getValue(),
                CommonModel.MTIME, startTime,
                CommonModel.PARAMS_CONFIG_STATUS, Arrays.asList(MerchantProviderParams.PARAMS_CONFIG_STATUS_PRE, MerchantProviderParams.PARAMS_CONFIG_STATUS_FAIL),
                "limit", limit
        );
        return merchantContractNPJdbcTemplate.query(sql, params, new BeanPropertyRowMapper<>(MerchantProviderParams.class));
    }


}
