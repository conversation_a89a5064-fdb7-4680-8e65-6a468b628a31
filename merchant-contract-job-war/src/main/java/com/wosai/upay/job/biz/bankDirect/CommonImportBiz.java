package com.wosai.upay.job.biz.bankDirect;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.cache.Cache;
import com.google.common.cache.CacheBuilder;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.shouqianba.cua.enums.contract.ProviderEnum;
import com.shouqianba.cua.enums.core.PaywayEnum;
import com.wosai.common.exception.CommonInvalidParameterException;
import com.wosai.common.exception.CommonPubBizException;
import com.wosai.common.utils.WosaiCollectionUtils;
import com.wosai.common.utils.WosaiMapUtils;
import com.wosai.common.utils.WosaiStringUtils;
import com.wosai.data.bean.BeanUtil;
import com.wosai.data.dao.DaoConstants;
import com.wosai.data.util.CollectionUtil;
import com.wosai.pantheon.util.MapUtil;
import com.wosai.trade.service.FeeRateService;
import com.wosai.trade.service.TradeComboDetailService;
import com.wosai.trade.service.request.ApplyFeeRateRequest;
import com.wosai.trade.service.result.TradeComboDetailResult;
import com.wosai.upay.core.model.MerchantConfig;
import com.wosai.upay.core.service.MerchantService;
import com.wosai.upay.core.service.SupportService;
import com.wosai.upay.core.service.TradeConfigService;
import com.wosai.upay.job.Constants.BankDirectApplyConstant;
import com.wosai.upay.job.adapter.apollo.ApplicationApolloConfig;
import com.wosai.upay.job.adapter.apollo.BatchTemplateApolloConfig;
import com.wosai.upay.job.biz.BusinessLogBiz;
import com.wosai.upay.job.biz.SubBizParamsBiz;
import com.wosai.upay.job.biz.bankDirect.afterSaveParams.AbstractAfterSaveParamsHandleBiz;
import com.wosai.upay.job.biz.bankDirect.commonImportPreparation.ImportPreSetup;
import com.wosai.upay.job.mapper.BankDirectApplyMapper;
import com.wosai.upay.job.mapper.ContractStatusMapper;
import com.wosai.upay.job.mapper.MerchantProviderParamsMapper;
import com.wosai.upay.job.model.ConfigWxCommonReq;
import com.wosai.upay.job.model.ContractStatus;
import com.wosai.upay.job.model.DO.BankDirectApply;
import com.wosai.upay.job.model.DO.MerchantProviderParams;
import com.wosai.upay.job.model.DO.MerchantProviderParamsExample;
import com.wosai.upay.job.model.InitBankAcquireInfoDTO;
import com.wosai.upay.job.providers.DefaultChangeTradeParamsBiz;
import com.wosai.upay.job.refactor.dao.McProviderDAO;
import com.wosai.upay.job.refactor.dao.MerchantProviderParamsDAO;
import com.wosai.upay.job.refactor.model.entity.McProviderDO;
import com.wosai.upay.job.refactor.model.entity.MerchantProviderParamsDO;
import com.wosai.upay.job.refactor.model.enums.UseStatusEnum;
import com.wosai.upay.job.service.AcquirerService;
import com.wosai.upay.job.util.CommonUtil;
import com.wosai.upay.merchant.contract.exception.ContractBizException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.PropertyPlaceholderHelper;

import java.util.*;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import static com.wosai.upay.job.biz.bankDirect.HxbImportBiz.rangeInDefined;
import static com.wosai.upay.job.biz.comboparams.ProviderParamsHandle.SYSTEM_CONFIG_NAME_PROVIDER_TRADE_PARAMS_KEY;

@Component
@Slf4j
public class CommonImportBiz {
    @Autowired
    private MerchantProviderParamsMapper merchantProviderParamsMapper;
    @Autowired
    private MerchantService merchantService;
    @Autowired
    private HxbImportBiz hxbImportBiz;
    @Autowired
    private DefaultChangeTradeParamsBiz defaultChangeTradeParamsBiz;
    @Autowired
    private SupportService supportService;
    @Autowired
    private SubBizParamsBiz subBizParamsBiz;
    @Autowired
    private FeeRateService feeRateService;
    @Autowired
    private McProviderDAO mcProviderDAO;
    @Autowired
    private ContractStatusMapper contractStatusMapper;
    @Autowired
    private BatchTemplateApolloConfig batchTemplateApolloConfig;
    @Autowired
    TradeComboDetailService tradeComboDetailService;
    @Lazy
    @Autowired
    private AcquirerService acquirerService;
    @Autowired
    private BankDirectApplyMapper bankDirectApplyMapper;
    @Autowired
    private ApplicationApolloConfig applicationApolloConfig;
    @Autowired
    private BusinessLogBiz businessLogBiz;
    @Autowired
    private List<AbstractAfterSaveParamsHandleBiz> afterSaveParamsHandleBizList;


    @Autowired
    private List<ImportPreSetup> importPreSetupList;
    @Autowired
    private MerchantProviderParamsDAO merchantProviderParamsDAO;
    @Autowired
    private TradeConfigService tradeConfigService;

    private static final List<Integer> SUPPORT_PAYWAYS = Arrays.asList(PaywayEnum.WEIXIN.getValue(), PaywayEnum.ALIPAY.getValue(), PaywayEnum.UNIONPAY.getValue());


    /**
     * 相关基础参数是否已经存在
     */
    private static final Cache<String, Boolean> CACHE = CacheBuilder.newBuilder()
            .maximumSize(128)
            .expireAfterWrite(7, TimeUnit.DAYS)
            .build();


    public void commonImport(List<String> row) {
        String merchantSn = row.get(0);
        try {
            Map configMap = batchTemplateApolloConfig.getSupportBankConfig();
            String provider = row.get(1);
            //provider
            Map<String, Object> map = WosaiMapUtils.getMap(configMap, provider);
            if (WosaiMapUtils.isEmpty(map)) {
                throw new CommonPubBizException("不支持未配置的 provider");
            }
            //是否需要导入收单机构基本规则配置
            Boolean initData  = WosaiMapUtils.getBooleanValue(map, "initBankAcquireData",Boolean.FALSE);
            try {
                if(initData && !CACHE.get(provider,() -> Boolean.FALSE)) {
                    initBankAcquireInfo(map, provider);
                    CACHE.put(provider,Boolean.TRUE);
                }
            } catch (ExecutionException e) {
                log.warn("commonImport 初始化数据异常");
            }
            Optional<McProviderDO> byProvider = mcProviderDAO.getByProvider(provider);
            if (!byProvider.isPresent()) {
                throw new ContractBizException("该provider对应的收单机构信息不存在");
            }
            String acquirer = byProvider.get().getAcquirer();
            String feeRate = row.get(6);
            String combIdString = row.get(7);

            //通用check
            commonImportPreCheck(row, map);
            //与收单机构对接进行预处理操作
            importPreSetupList.forEach(importPreSetup -> {
                if (importPreSetup.needSetup(provider)) {
                    importPreSetup.setup(row);
                }
            });

            //删除老参数
            deleteParams(merchantSn, Integer.parseInt(provider));

            //----保存交易参数
            //银行参数  payway为 0,2,3...的. key为payway,value为Map
            List<Integer> payways = new ArrayList<>();
            for (Map.Entry<String, Object> entry : map.entrySet()) {
                MerchantProviderParams params = buildMerchantProviderParams(row, entry.getValue(), entry.getKey());
                if (Objects.nonNull(params)) {
                    payways.add(params.getPayway());
                    merchantProviderParamsMapper.insertSelective(params);
                }
            }

            //微信目录
            for (AbstractAfterSaveParamsHandleBiz afterSaveParamsHandleBiz : afterSaveParamsHandleBizList) {
                if (afterSaveParamsHandleBiz.supportProvider(provider)) {
                    afterSaveParamsHandleBiz.configWx(new ConfigWxCommonReq().setMerchantSn(merchantSn).setWeixinMchId(row.get(3)));
                }
            }
            // 如果需要校验微信或者支付宝子商户号的授权状态，就需要写一个进件成功的BankDirectApply数据
            if (WosaiMapUtils.getBooleanValue(map, BankDirectApplyConstant.Extra.CHECK_WEIXIN_AUTH, false) || WosaiMapUtils.getBooleanValue(map, BankDirectApplyConstant.Extra.CHECK_ALI_AUTH, false)) {
                saveContractSuccessApply(merchantSn, provider, acquirer, feeRate, combIdString, payways, map);
                return;
            }
            if (applicationApolloConfig.getCommonImportToSystemAcquirerChangeSwitch()) {
                operateAcquirerChangeWithDelay(combIdString, map, merchantSn, feeRate, provider);
            } else {
                //切换交易参数
                changeParams(merchantSn, Integer.parseInt(provider), feeRate);

                //更新contract_status

                ContractStatus contractStatus = contractStatusMapper.selectByMerchantSn(merchantSn);
                if (contractStatus != null) {
                    contractStatusMapper.updateByPrimaryKeySelective(
                            new ContractStatus().setId(contractStatus.getId())
                                    .setAcquirer(WosaiStringUtils.isNotEmpty(acquirer) ? acquirer : provider)
                    );
                }
                //设置套餐
                if (WosaiStringUtils.isNotEmpty(combIdString)) {
                    setComb(merchantSn, Long.parseLong(combIdString), "1", feeRate);
                }
            }
        } catch (Exception e) {
           log.error("commonImport异常,merchantSn:{},异常信息:{}", merchantSn, e);
           throw new RuntimeException(e.getMessage());
        }
    }

    private void saveContractSuccessApply(String merchantSn, String provider, String acquirer, String feeRate, String combIdString, List<Integer> payways, Map config) {
        String devCode = WosaiMapUtils.getString(config, "devCode");
        Integer bankRef = WosaiMapUtils.getInteger(config, "bankRef");
        BankDirectApply applyBySnAndDevCode = bankDirectApplyMapper.getApplyBySnAndDevCode(merchantSn, devCode);
        if (applyBySnAndDevCode != null) {
            bankDirectApplyMapper.deleteByPrimaryKey(applyBySnAndDevCode.getId());
        }

        // 设置套餐时要看这个配置了哪些参数，如果只有支付宝则去设置支付宝套餐，如果只有微信则去设置微信套餐
        List<Map> merchantConfig = payways.stream().filter(SUPPORT_PAYWAYS::contains).map(r -> CollectionUtil.hashMap(
                "status", MerchantConfig.STATUS_OPENED,
                "rate", feeRate,
                "payway", r
        )).collect(Collectors.toList());
        //保存申请单
        BankDirectApply bankDirectApply = new BankDirectApply()
                .setMerchant_sn(merchantSn)
                .setDev_code(devCode)
                .setBank_ref(bankRef)
                .setStatus(BankDirectApplyConstant.Status.APPLYING)
                .setForm_body(JSON.toJSONString(CollectionUtil.hashMap("merchant_config", merchantConfig,
                        "from", "import",
                        "trade_combo_id", Long.parseLong(combIdString))))
                //认为进件成功
                .setProcess_status(BankDirectApplyConstant.ProcessStatus.CONTRACT_SUCCESS)
                .setExtra(JSON.toJSONString(CollectionUtil.hashMap(
                        BankDirectApplyConstant.Extra.PROVIDER, Integer.parseInt(provider),
                        BankDirectApplyConstant.Extra.ACQUIRE, acquirer,
                        BankDirectApplyConstant.Extra.CHECK_WEIXIN_AUTH, WosaiMapUtils.getBooleanValue(config, "checkWeixinAuth", false),
                        BankDirectApplyConstant.Extra.CHECK_ALI_AUTH, WosaiMapUtils.getBooleanValue(config, "checkAliAuth", false))));
        bankDirectApplyMapper.insertSelective(bankDirectApply);
        businessLogBiz.recordBankLog(bankDirectApply);
    }

    /**
     * 导入的数据插入mc_acquirer_change表中进行管理切换
     */
    private void operateAcquirerChangeWithDelay(String combIdString, Map<String, Object> map, String merchantSn, String feeRate, String provider) {
        // 导入到切换收单机构里面记录,定时去执行切换收单机构
        Optional<McProviderDO> mcProviderDOOptional = mcProviderDAO.getByProvider(provider);
        boolean acquirerChange = acquirerService.applyChangeAcquirer(merchantSn, mcProviderDOOptional.get().getAcquirer());
        if (!acquirerChange) {
            throw new CommonPubBizException("商户" + merchantSn + ",切换收单机构失败");
        }
        String devCode = WosaiMapUtils.getString(map, "devCode");
        Integer bankRef = WosaiMapUtils.getInteger(map, "bankRef");
        if (WosaiStringUtils.isEmpty(combIdString)) {
            return;
        }
        //银行直连申请表设置下
        BankDirectApply bankDirectApply = bankDirectApplyMapper.getApplyBySnAndDevCode(merchantSn, devCode);

        if (Objects.isNull(bankDirectApply) || Objects.equals(bankDirectApply.getStatus(), BankDirectApplyConstant.Status.FAIL)) {
            // 创建一个成功的
            BankDirectApply record = new BankDirectApply();
            record.setMerchant_sn(merchantSn);
            record.setDev_code(devCode);
            record.setBank_ref(bankRef);
            record.setStatus(BankDirectApplyConstant.Status.SUCCESS);
            record.setResult("excel线下导入数据");
            record.setCreate_at(new Date());
            record.setUpdate_at(new Date());
            record.setPriority(new Date());
            record.setProcess_status(BankDirectApplyConstant.ProcessStatus.SUCCESS);
            record.setForm_body(JSONObject.toJSONString(CollectionUtil.hashMap(
                    "merchant_config", Lists.newArrayList(
                            CollectionUtil.hashMap("payway", String.valueOf(PaywayEnum.ALIPAY.getValue()), "rate", feeRate),
                            CollectionUtil.hashMap("payway", String.valueOf(PaywayEnum.WEIXIN.getValue()), "rate", feeRate),
                            CollectionUtil.hashMap("payway", String.valueOf(PaywayEnum.UNIONPAY.getValue()), "rate", feeRate)
                    ),
                    "trade_combo_id", Long.parseLong(combIdString)
            )));
            bankDirectApplyMapper.insertSelective(record);
        } else {
            // 可以修改下数据，比如费率套餐
            BankDirectApply update = new BankDirectApply();
            update.setId(bankDirectApply.getId());
            JSONObject formBody = JSONObject.parseObject(bankDirectApply.getForm_body());
            formBody.put("merchant_config", Lists.newArrayList(
                    CollectionUtil.hashMap("payway", String.valueOf(PaywayEnum.ALIPAY.getValue()), "rate", feeRate),
                    CollectionUtil.hashMap("payway", String.valueOf(PaywayEnum.WEIXIN.getValue()), "rate", feeRate),
                    CollectionUtil.hashMap("payway", String.valueOf(PaywayEnum.UNIONPAY.getValue()), "rate", feeRate)
            ));
            formBody.put("trade_combo_id", Long.parseLong(combIdString));
            update.setForm_body(JSONObject.toJSONString(formBody));
            bankDirectApplyMapper.updateByPrimaryKeySelective(update);
        }
    }


    public void deleteParams(String merchantSn, int provider) {
        MerchantProviderParamsExample example = new MerchantProviderParamsExample();
        example.or().andMerchant_snEqualTo(merchantSn)
                .andProviderEqualTo(provider);
        merchantProviderParamsMapper.deleteByExample(example);
    }

    private static final PropertyPlaceholderHelper helper = new PropertyPlaceholderHelper("${", "}", "", false);

    public MerchantProviderParams buildMerchantProviderParams(List<String> row, Object configObject, String payWay) {
        if (!payWay.matches("[0-9]+")) {
            return null;
        }
        Map<String, Object> config = (Map<String, Object>) configObject;
        Properties properties = new Properties();
        for (int i = 0; i < row.size(); i++) {
            String value = row.get(i);
            if (Objects.isNull(value)) {
                properties.setProperty(String.valueOf(i), StringUtils.EMPTY);
            } else {
                properties.setProperty(String.valueOf(i), value);
            }
        }
        // 占位符替换
        extractMap(config, properties);
        // extra中字段处理
        Map<String, Object> tradeParams = (Map<String, Object>) config.remove("tradeParams");
        if (WosaiMapUtils.isEmpty(tradeParams)) {
            throw new CommonPubBizException("交易参数tradeParams配置为空");
        }
        Map<String, Object> extra = (Map<String, Object>) config.remove("extra");
        if (WosaiMapUtils.isEmpty(extra)) {
            extra = new HashMap<>();
        }
        extra.put("tradeParams", tradeParams);

        MerchantProviderParams params = JSON.parseObject(JSON.toJSONString(config), MerchantProviderParams.class);
        String id = UUID.randomUUID().toString();
        long timeMillis = System.currentTimeMillis();
        params.setId(id)
                .setMerchant_sn(row.get(0))
                .setProvider(Integer.parseInt(row.get(1)))
                .setPayway(Integer.parseInt(payWay))
                .setParams_config_status(MerchantProviderParams.PARAMS_CONFIG_STATUS_NULL)
                .setStatus(UseStatusEnum.NO_USE.getValue())
                .setExtra(CommonUtil.map2Bytes(extra))
                .setUpdate_status(1)
                .setCtime(timeMillis)
                .setMtime(timeMillis);

        //支付宝和微信需要设置的值
        if (PaywayEnum.ALIPAY.getValue().toString().equals(payWay) || PaywayEnum.WEIXIN.getValue().toString().equals(payWay)) {
            Map merchant = merchantService.getMerchantBySn(row.get(0));
            HxbImportBiz.PaywayInfo paywayInfo = hxbImportBiz.getPaywayInfo(merchant);
            params.setMerchant_name(paywayInfo.getMerchantName());
            if (PaywayEnum.ALIPAY.getValue().toString().equals(payWay)) {
                params.setAli_mcc(paywayInfo.getMcc());
            } else {
                params.setWx_settlement_id(paywayInfo.getWxSettlementId());
            }

        }
        return params;
    }

    private void extractMap(Map<String, Object> map, Properties properties) {
        for (Map.Entry<String, Object> entry : map.entrySet()) {
            String key = entry.getKey();
            Object value = entry.getValue();
            if (value instanceof Map) {
                extractMap((Map) value, properties);
            }
            value = getRealValue(value, properties);
            map.put(key, value);
        }
    }

    private Object getRealValue(Object value, Properties properties) {
        if (value instanceof String) {
            String stringValue = (String) value;
            value = helper.replacePlaceholders(stringValue, properties);
        }
        return value;
    }

    public void changeParams(String merchantSn, Integer provider, String fee) {
        MerchantProviderParamsExample example = new MerchantProviderParamsExample();
        example.or().andMerchant_snEqualTo(merchantSn)
                .andProviderEqualTo(provider)
                .andPaywayNotEqualTo(PaywayEnum.ACQUIRER.getValue())
                .andDeletedEqualTo(false);
        example.setOrderByClause("ctime asc");
        List<MerchantProviderParams> merchantProviderParams = merchantProviderParamsMapper.selectByExample(example);
        if (WosaiCollectionUtils.isEmpty(merchantProviderParams)) {
            throw new CommonPubBizException("无指定参数");
        }


        //将参数排序,微信放在首位,优先设置微信参数,如果失败就可以避免其他参数出现问题
        final ArrayList<MerchantProviderParams> paramsList = Lists.newArrayList();
        merchantProviderParams.forEach(param -> {
            if (Objects.equals(param.getPayway(), PaywayEnum.WEIXIN.getValue())) {
                paramsList.add(0, param);
            } else {
                paramsList.add(param);
            }
        });

        List<MerchantProviderParams> waitRollBackIfException = new ArrayList<>();
        try {
            for (MerchantProviderParams providerParams : paramsList) {

                List<MerchantProviderParams> usingParams = getUsingParams(providerParams);
                defaultChangeTradeParamsBiz.changeTradeParamsWithoutCheckAcquirer(providerParams, fee, false, "1");
                //更新status字段
                updateParamsStatusAfterChange(providerParams);
                //没有异常则认为切换成功
                if (WosaiCollectionUtils.isNotEmpty(usingParams)) {
                    waitRollBackIfException.addAll(usingParams);
                }

            }
        } catch (Exception e) {
            log.error("切换参数异常,进行回滚操作: params: {}", JSON.toJSONString(waitRollBackIfException), e);
            //恢复数据
            for (MerchantProviderParams rollBack : waitRollBackIfException) {
                try {
                    defaultChangeTradeParamsBiz.changeTradeParamsWithoutCheckAcquirer(rollBack, null, false, "1");
                    updateParamsStatusAfterChange(rollBack);
                } catch (Exception ex) {
                    log.error("切参数异常恢复老参数异常,参数id: {}", rollBack.getId(), ex);
                }
            }
            throw e;
        }

    }

    private void updateParamsStatusAfterChange(MerchantProviderParams merchantProviderParams) {
        MerchantProviderParamsExample merchantProviderParamsExample = new MerchantProviderParamsExample();
        merchantProviderParamsExample.or()
                .andMerchant_snEqualTo(merchantProviderParams.getMerchant_sn())
                .andPaywayEqualTo(merchantProviderParams.getPayway())
                .andIdNotEqualTo(merchantProviderParams.getId())
                .andStatusEqualTo(com.shouqianba.cua.enums.status.UseStatusEnum.IN_USE.getValue())
                .andProviderNotEqualTo(merchantProviderParams.getPayway())
                .andDeletedEqualTo(false);
        List<MerchantProviderParams> upList = merchantProviderParamsMapper.selectByExample(merchantProviderParamsExample);
        //设置当前子商户号为已使用
        upList.forEach(u -> {
            merchantProviderParamsMapper.updateStatusByKey(com.shouqianba.cua.enums.status.UseStatusEnum.NO_USE.getValue(), System.currentTimeMillis(), u.getId());
        });
        merchantProviderParamsMapper.updateStatusByKey(com.shouqianba.cua.enums.status.UseStatusEnum.IN_USE.getValue(), System.currentTimeMillis(), merchantProviderParams.getId());
    }

    public List<MerchantProviderParams> getUsingParams(MerchantProviderParams merchantProviderParam) {
        //当前商户该payWay下status=1的
        MerchantProviderParamsExample merchantProviderParamsExample = new MerchantProviderParamsExample();
        merchantProviderParamsExample.or()
                .andMerchant_snEqualTo(merchantProviderParam.getMerchant_sn())
                .andPaywayEqualTo(merchantProviderParam.getPayway())
                .andStatusEqualTo(com.shouqianba.cua.enums.status.UseStatusEnum.IN_USE.getValue())
                .andProviderNotEqualTo(merchantProviderParam.getPayway())
                .andDeletedEqualTo(false);
        List<MerchantProviderParams> upList = merchantProviderParamsMapper.selectByExample(merchantProviderParamsExample);
        return upList;
    }

    public void setComb(String merchantSn, Long combId, String tradeAppId, String feeRate) {
        if (combId == null) return;
        Map<String, String> applyFeeRateMap = null;

        if (WosaiStringUtils.isNotEmpty(feeRate)) {
            //设置套餐
            applyFeeRateMap = CollectionUtil.hashMap(
                    String.valueOf(PaywayEnum.ALIPAY.getValue()), feeRate,
                    String.valueOf(PaywayEnum.WEIXIN.getValue()), feeRate,
                    String.valueOf(PaywayEnum.UNIONPAY.getValue()), feeRate
            );
        }
        ApplyFeeRateRequest applyFeeRateRequest = new ApplyFeeRateRequest()
                .setMerchantSn(merchantSn)
                .setAuditSn(String.format("通用银行线下导入%s", subBizParamsBiz.getTradeAppNameById(tradeAppId)))
                .setTradeComboId(combId)
                .setApplyFeeRateMap(applyFeeRateMap);
        try {
            feeRateService.applyFeeRateOne(applyFeeRateRequest);
            supportService.removeCachedParams(merchantSn);
        } catch (Exception e) {
            log.error("通用银行线下导入设置多业务套餐异常,merchantSn:{},tradeAppId:{},comboId:{},异常信息:{}", merchantSn, tradeAppId, combId, e);
        }

    }

    public void commonImportPreCheck(List<String> row, Map map) {
        String merchantSn = row.get(0);
        String feeRate = row.get(6);
        String combIdString = row.get(7);

        Map merchant = merchantService.getMerchantByMerchantSn(merchantSn);
        if (WosaiMapUtils.isEmpty(merchant)) {
            throw new CommonPubBizException("商户不存在");
        }

        //民生银行必须传云闪付参数
        if (StringUtils.isEmpty(row.get(5)) && String.valueOf(ProviderEnum.PROVIDER_CMBC.getValue()).equals(row.get(1))) {
            throw new CommonPubBizException("请填写云闪付参数");
        }

        if (WosaiStringUtils.isNotEmpty(feeRate)) {
            Double fee_rate_double = Double.valueOf(feeRate);
            if (fee_rate_double < 0) {
                throw new RuntimeException("费率取值应大于等于0");
            }
        }

        if (WosaiStringUtils.isEmpty(combIdString)) {
            throw new CommonPubBizException("套餐不存在");
        }

        if (WosaiStringUtils.isNotEmpty(row.get(8)) || WosaiStringUtils.isNotEmpty(row.get(9))) {
            Map bankAccount = merchantService.getMerchantBankAccountByMerchantId(MapUtil.getString(merchant, "id"));
            String holder = MapUtil.getString(bankAccount, "holder");
            String identity = MapUtil.getString(bankAccount, "identity");
            if (WosaiStringUtils.isNotEmpty(row.get(8)) && !(WosaiStringUtils.equals(row.get(8), holder))) {
                throw new CommonInvalidParameterException("账户持有人名称不相符");
            }
            if (WosaiStringUtils.isNotEmpty(row.get(9)) && !identity.endsWith(row.get(9))) {
                throw new CommonInvalidParameterException("账户持有人证件号不相符");
            }
            if (applicationApolloConfig.getCommonImportToSystemAcquirerChangeSwitch()) {
                // provider所对应的银行 与 默认结算银行卡是否一致
                String providerToBankName = WosaiMapUtils.getString(map, "appointBankName");
                if (StringUtils.isNotEmpty(providerToBankName)) {
                    String bankName = WosaiMapUtils.getString(bankAccount, "bank_name");
                    if (!Objects.equals(providerToBankName, bankName)) {
                        throw new CommonPubBizException("该商户尚未绑定该合作银行卡结算卡");
                    }
                }
            }

        }


        if (WosaiStringUtils.isNotEmpty(combIdString) && WosaiStringUtils.isNotEmpty(feeRate)) {
            List<TradeComboDetailResult> detailResults = tradeComboDetailService.listByComboId(Long.parseLong(combIdString));
            detailResults.forEach(detail -> {
                String feeRateMax = detail.getFeeRateMax();
                String feeRateMin = detail.getFeeRateMin();

                boolean range = rangeInDefined(Double.valueOf(feeRate), Double.valueOf(feeRateMin), Double.valueOf(feeRateMax));
                if (!range) {
                    throw new CommonInvalidParameterException("导入失败、手续费费率有误");
                }
            });
        }
    }

    /**
     * 初始化银行导入通用数据
     * @param map
     * @param provider
     */
    private void initBankAcquireInfo(Map<String, Object> map, String provider) {
        final InitBankAcquireInfoDTO infoDTO = new InitBankAcquireInfoDTO();
        infoDTO.setProvider(provider);
        String acquire = WosaiMapUtils.getString(map, "acquire");
        if (StringUtils.isEmpty(acquire)) {
            // 找到0对应的groupId,这个就是对应的acquire
            acquire = (String) BeanUtil.getNestedProperty(map, "0.rule_group_id");
        }
        infoDTO.setAcquire(acquire);
        String acquireCHName = WosaiMapUtils.getString(map, "appointBankName");
        infoDTO.setAcquireCHName(acquireCHName);
        // 作为通用导入
        infoDTO.setMcProviderBeanName("common");
        // 支持的payWay有哪些不包括0
        final List<Integer> payWayList = map.keySet().stream()
                .filter(payWay -> payWay.matches("[0-9]+"))
                .filter(payWay ->!Objects.equals(payWay,String.valueOf(PaywayEnum.ACQUIRER.getValue())))
                .map(way -> Integer.valueOf(way))
                .collect(Collectors.toList());
        infoDTO.setPayWayList(payWayList);
        Map<Integer, String> payWayChannelNoMap = map.entrySet().stream()
                .filter(entry -> entry.getKey().matches("[0-9]+"))
                .collect(Collectors.toMap(t -> Integer.valueOf(t.getKey()), t -> BeanUtil.getPropString(t.getValue(), "channel_no")));
        infoDTO.setPayWayChannelNoMap(payWayChannelNoMap);
        acquirerService.initBankAcquireData(infoDTO);
    }

    @Transactional(rollbackFor = Exception.class)
    public void indirectCommonImport(List<String> row) {{
        String merchantSn = row.get(0);
        try {
            Map configMap = batchTemplateApolloConfig.getSupportIndirectConfig();
            String provider = row.get(1);
            //provider
            Map<String, Object> map = WosaiMapUtils.getMap(configMap, provider);
            if (WosaiMapUtils.isEmpty(map)) {
                throw new CommonPubBizException("不支持未配置的 provider");
            }

            Optional<McProviderDO> byProvider = mcProviderDAO.getByProvider(provider);
            if (!byProvider.isPresent()) {
                throw new ContractBizException("该provider对应的收单机构信息不存在");
            }
            String feeRate = row.get(6);
            String combIdString = row.get(7);

            //通用check
            indirectImportPreCheck(row, map);
            //删除老参数
            deleteParams(merchantSn, Integer.parseInt(provider));
            //当前配置支持的支付方式,注意不包括0
            final List<String> payWayList = map.keySet().stream()
                    .filter(payWay -> payWay.matches("[0-9]+"))
                    .filter(payWay ->!Objects.equals(payWay,String.valueOf(PaywayEnum.ACQUIRER.getValue())))
                    .collect(Collectors.toList());
            //设置当前商户对应的payway正在使用
            setStatusNoUse(merchantSn,payWayList);
            //保存参数 payway为 0,2,3...并且将支付源的设为正在使用
            List<Integer> payways = new ArrayList<>();
            //要保存的参数数据
            final Map<String, MerchantProviderParams> paywayParams = Maps.newHashMap();
            for (Map.Entry<String, Object> entry : map.entrySet()) {
                MerchantProviderParams params = buildMerchantProviderParams(row, entry.getValue(), entry.getKey());
                if (Objects.nonNull(params)) {
                    payways.add(params.getPayway());
                    if(!Objects.equals(params.getPayway(),PaywayEnum.ACQUIRER.getValue())) {
                       params.setStatus(UseStatusEnum.IN_USE.getValue());
                    }
                    merchantProviderParamsMapper.insertSelective(params);
                    paywayParams.put(String.valueOf(params.getPayway()),params);
                }
            }
            //设置套餐
            if (WosaiStringUtils.isNotEmpty(combIdString)) {
                setIndirectCombe(merchantSn, Long.parseLong(combIdString), "1", feeRate,payWayList);
            }
            //将交易参数写入merchant_config
            updateMerchantConfigParams(merchantSn, paywayParams, payWayList,Integer.valueOf(provider),map);
        } catch (Exception e) {
            log.error("commonImport异常,merchantSn:{},异常信息", merchantSn, e);
            throw new CommonPubBizException(e.getMessage());
        }
    }

    }

    private void updateMerchantConfigParams(String merchantSn, Map<String, MerchantProviderParams> paywayParamsMap, List<String> payWayList, Integer provider, Map<String, Object> configMap) {
        Map<String, Object> providerTradeParamsKey = tradeConfigService.getSystemConfigContentByName(SYSTEM_CONFIG_NAME_PROVIDER_TRADE_PARAMS_KEY);
        String tradeParamKey = BeanUtil.getPropString(providerTradeParamsKey, String.valueOf(provider));
        final Map merchant = merchantService.getMerchantByMerchantSn(merchantSn);
        final String merchantId = WosaiMapUtils.getString(merchant, DaoConstants.ID);
        payWayList.stream().forEach(payway -> {
            Map params = WosaiMapUtils.getMap(configMap, payway.toString());
            final String agentName = WosaiMapUtils.getString(params, "agentName");
            final MerchantProviderParams merchantProviderParams = paywayParamsMap.get(payway);
            Map bytes2Map = CommonUtil.bytes2Map(merchantProviderParams.getExtra());
            Map tradeParam = CollectionUtil.hashMap(tradeParamKey, bytes2Map.get("tradeParams"));
            final Map oldConfig = tradeConfigService.getMerchantConfigByMerchantIdAndPayway(merchantId, Integer.valueOf(payway));
            final Map newConfig= CollectionUtil.hashMap(
                    MerchantConfig.MERCHANT_ID, merchantId,
                    MerchantConfig.PAYWAY, Integer.valueOf(payway),
                    MerchantConfig.B2C_FORMAL, MerchantConfig.STATUS_CLOSED,
                    MerchantConfig.C2B_FORMAL, MerchantConfig.STATUS_CLOSED,
                    MerchantConfig.WAP_FORMAL, MerchantConfig.STATUS_CLOSED,
                    MerchantConfig.MINI_FORMAL, MerchantConfig.STATUS_CLOSED,
                    MerchantConfig.APP_FORMAL, MerchantConfig.STATUS_CLOSED,
                    MerchantConfig.EXTEND2_FORMAL, MerchantConfig.STATUS_CLOSED,
                    MerchantConfig.B2C_AGENT_NAME, agentName,
                    MerchantConfig.C2B_AGENT_NAME, agentName,
                    MerchantConfig.WAP_AGENT_NAME, agentName,
                    MerchantConfig.MINI_AGENT_NAME, agentName,
                    MerchantConfig.APP_AGENT_NAME, agentName,
                    MerchantConfig.PROVIDER, provider,
                    MerchantConfig.PARAMS, tradeParam
            );
            if(Objects.nonNull(oldConfig)) {
                newConfig.put(DaoConstants.ID, oldConfig.get(DaoConstants.ID));
                tradeConfigService.updateMerchantConfig(newConfig);
            }else {
                tradeConfigService.createMerchantConfig(newConfig);
            }

        });
        //删除交易网关缓存
        supportService.removeCachedParams(merchantSn);
    }


    /**
     * 设置商户正在使用的参数为未使用
     * @param merchantSn 商户号
     * @param payWayList 餐数集合
     */
    private void setStatusNoUse(String merchantSn, List<String> payWayList) {
        //符合当前paywayList且正在使用的交易参数
        final List<MerchantProviderParamsDO> usingParamsList = merchantProviderParamsDAO.getUsingBySnAndPayWayList(merchantSn,
                payWayList.stream().map(payway -> Integer.valueOf(payway)).collect(Collectors.toList()));
        //将usingParamsList中的状态变为未使用
        if(!usingParamsList.isEmpty()) {
            final List<MerchantProviderParamsDO> noUsingList = usingParamsList.stream().map(params -> {
                MerchantProviderParamsDO merchantProviderParamsDO = new MerchantProviderParamsDO();
                merchantProviderParamsDO.setId(params.getId());
                merchantProviderParamsDO.setStatus(UseStatusEnum.NO_USE.getValue());
                return merchantProviderParamsDO;
            }).collect(Collectors.toList());
            merchantProviderParamsDAO.batchUpdateByIdSelective(noUsingList);
        }
    }


    /**
     * 设置间连多业务套餐
     * @param merchantSn 商户号
     * @param combId 套餐ID
     * @param tradeAppId 业务方ID
     * @param feeRate 费率
     * @param payWayList 支持的支付方式 [2,3,17]等
     */
    public void setIndirectCombe(String merchantSn, Long combId, String tradeAppId, String feeRate, List<String> payWayList) {
        if (combId == null) return;
        Map<String, String> applyFeeRateMap;

        if (WosaiStringUtils.isNotEmpty(feeRate)) {
            //设置套餐
            applyFeeRateMap = CollectionUtil.hashMap(
                    String.valueOf(PaywayEnum.ALIPAY.getValue()), feeRate,
                    String.valueOf(PaywayEnum.WEIXIN.getValue()), feeRate,
                    String.valueOf(PaywayEnum.UNIONPAY.getValue()), feeRate
            );
            payWayList.forEach(payway ->{
                applyFeeRateMap.putIfAbsent(payway,feeRate);
            });

        } else {
            applyFeeRateMap = null;
        }
        ApplyFeeRateRequest applyFeeRateRequest = new ApplyFeeRateRequest()
                .setMerchantSn(merchantSn)
                .setAuditSn(String.format("通用间连线下导入%s", subBizParamsBiz.getTradeAppNameById(tradeAppId)))
                .setTradeComboId(combId)
                .setApplyFeeRateMap(applyFeeRateMap);
        try {
            feeRateService.applyFeeRateOne(applyFeeRateRequest);
            supportService.removeCachedParams(merchantSn);
        } catch (Exception e) {
            log.error("通用间连线下导入套餐异常,merchantSn:{},tradeAppId:{},comboId:{},异常信息", merchantSn, tradeAppId, combId, e);
            throw new CommonPubBizException(e.getMessage());
        }

    }




    public void indirectImportPreCheck(List<String> row, Map map) {
        String merchantSn = row.get(0);
        String feeRate = row.get(6);
        String combIdString = row.get(7);
        //收单机构商户号
        final String acquireNo = StrUtil.trim(row.get(2));
        if(StrUtil.isEmpty(acquireNo)) {
            throw new CommonPubBizException("收单机构商户号不存在");
        }
        //微信子商户号
        final String wxNo = StrUtil.trim(row.get(3));
        //支付宝子商户号
        final String aliNo = StrUtil.trim(row.get(4));
        //云闪付子商户号
        final String unionNo = StrUtil.trim(row.get(5));

        if(StrUtil.isAllBlank(wxNo, aliNo, unionNo)) {
            throw new CommonPubBizException("请根据实际需要填写微信、支付宝、云闪付子商户号中的一个");
        }

        Map merchant = merchantService.getMerchantByMerchantSn(merchantSn);
        if (WosaiMapUtils.isEmpty(merchant)) {
            throw new CommonPubBizException("商户不存在");
        }

        if (WosaiStringUtils.isNotEmpty(feeRate)) {
            Double fee_rate_double = Double.valueOf(feeRate);
            if (fee_rate_double < 0) {
                throw new RuntimeException("费率取值应大于等于0");
            }
        }

        if (WosaiStringUtils.isNotEmpty(row.get(8)) || WosaiStringUtils.isNotEmpty(row.get(9))) {
            Map bankAccount = merchantService.getMerchantBankAccountByMerchantId(MapUtil.getString(merchant, "id"));
            String holder = MapUtil.getString(bankAccount, "holder");
            String identity = MapUtil.getString(bankAccount, "identity");
            if (WosaiStringUtils.isNotEmpty(row.get(8)) && !(WosaiStringUtils.equals(row.get(8), holder))) {
                throw new CommonInvalidParameterException("账户持有人名称不相符");
            }
            if (WosaiStringUtils.isNotEmpty(row.get(9)) && !identity.endsWith(row.get(9))) {
                throw new CommonInvalidParameterException("账户持有人证件号不相符");
            }

        }
        if (WosaiStringUtils.isNotEmpty(combIdString) && WosaiStringUtils.isNotEmpty(feeRate)) {
            List<TradeComboDetailResult> detailResults = tradeComboDetailService.listByComboId(Long.parseLong(combIdString));
            detailResults.forEach(detail -> {
                String feeRateMax = detail.getFeeRateMax();
                String feeRateMin = detail.getFeeRateMin();

                boolean range = rangeInDefined(Double.valueOf(feeRate), Double.valueOf(feeRateMin), Double.valueOf(feeRateMax));
                if (!range) {
                    throw new CommonInvalidParameterException("导入失败、手续费费率有误");
                }
            });
        }
    }

}
