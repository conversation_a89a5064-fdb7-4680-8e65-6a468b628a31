package com.wosai.upay.job.providers;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Maps;
import com.shouqianba.cua.enums.contract.ContractSubTaskProcessStatusEnum;
import com.shouqianba.cua.enums.contract.ProviderEnum;
import com.shouqianba.cua.enums.core.DeleteStatusEnum;
import com.shouqianba.cua.enums.core.PaywayEnum;
import com.shouqianba.cua.utils.object.ConvertUtil;
import com.shouqianba.cua.utils.object.ObjectExtensionUtils;
import com.wosai.common.utils.WosaiMapUtils;
import com.wosai.data.bean.BeanUtil;
import com.wosai.upay.common.dao.DaoConstants;
import com.wosai.upay.core.model.MerchantConfig;
import com.wosai.upay.core.model.TransactionParam;
import com.wosai.upay.job.enume.ProviderTerminalTaskTypeEnum;
import com.wosai.upay.job.handlers.ContractSubTaskHandler;
import com.wosai.upay.job.mapper.MerchantProviderParamsMapper;
import com.wosai.upay.job.model.*;
import com.wosai.upay.job.model.DO.MerchantProviderParams;
import com.wosai.upay.job.model.application.CommonResult;
import com.wosai.upay.job.refactor.dao.ContractSubTaskDAO;
import com.wosai.upay.job.refactor.dao.MerchantProviderParamsDAO;
import com.wosai.upay.job.refactor.model.bo.LklOpenUnionPayTradeParamBO;
import com.wosai.upay.job.refactor.model.entity.ContractSubTaskDO;
import com.wosai.upay.job.refactor.model.entity.MerchantProviderParamsDO;
import com.wosai.upay.job.refactor.model.entity.MerchantProviderParamsExtDO;
import com.wosai.upay.job.refactor.model.enums.ConfigStatusEnum;
import com.wosai.upay.job.refactor.model.enums.ContractSubTaskTypeEnum;
import com.wosai.upay.job.util.ProviderUtil;
import com.wosai.upay.merchant.contract.exception.ContractBizException;
import com.wosai.upay.merchant.contract.exception.ContractSysException;
import com.wosai.upay.merchant.contract.model.ContractResponse;
import com.wosai.upay.merchant.contract.model.WeixinConfig;
import com.wosai.upay.merchant.contract.model.terminal.LogOutTermInfoDTO;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.*;
import java.util.function.Function;

/**
 * lkl开放平台结算通道
 *
 * <AUTHOR>
 * @date 2024/1/12 15:36
 */
@Slf4j
@Component(LklOpenProvider.BEAN_NAME)
public class LklOpenProvider extends AbstractProvider {

    public LklOpenProvider() {
        super();
        initPayWayTaskConsumerMap(payWayTaskConsumerMap);
    }

    private void initPayWayTaskConsumerMap(Map<Integer, Function<TaskParamBO, ContractResponse>> map) {
        map.put(PaywayEnum.UNIONPAY.getValue(), this::processUnionPayInsertTask);
        map.put(PaywayEnum.JD_WALLET.getValue(), this::processJdInsertTask);
    }


    private final Map<Integer, Function<TaskParamBO, ContractResponse>> payWayTaskConsumerMap = Maps.newHashMap();


    public static final String BEAN_NAME = "lklOpen";

    @Resource
    private ContractSubTaskDAO contractSubTaskDAO;

    @Resource
    private MerchantProviderParamsDAO merchantProviderParamsDAO;

    @Resource
    private LklV3Provider lklV3Provider;


    @Override
    protected ContractSubTask produceUpdateTaskByRule(String merchantSn, ContractEvent event, Map<String, Object> paramContext, ContractRule contractRule) {
        // not support yet
        return null;
    }

    @Data
    @AllArgsConstructor
    private class TaskParamBO {
        private ContractTask contractTask;
        private ContractChannel contractChannel;
        private ContractSubTask subTask;
    }


    @Override
    public ContractResponse processInsertTaskByRule(ContractTask contractTask, ContractChannel contractChannel, ContractSubTask subTask) {
        if (!payWayTaskConsumerMap.containsKey(subTask.getPayway())) {
            throw new ContractBizException("无效的支付源, payWay = " + subTask.getPayway() + " subTask id = " + subTask.getId());
        }
        return payWayTaskConsumerMap.get(subTask.getPayway()).apply(new TaskParamBO(contractTask, contractChannel, subTask));
    }

    @Override
    protected ContractResponse processUpdateTaskByRule(ContractTask contractTask, ContractChannel contractChannel, ContractSubTask sub) {
        // not support yet
        return null;
    }

    @Override
    protected Map doWeixinSubDevConfig(WeixinConfig weixinConfig, MerchantProviderParams merchantProviderParams) {
        throw new ContractBizException(getProviderBeanName() + "暂不支持配置微信appid");
    }

    /**
     * 处理拉卡拉开放平台-银联 新增商户入网任务
     *
     * @param taskParamBO 任务参数
     * @return 任务处理结果
     */
    private ContractResponse processUnionPayInsertTask(TaskParamBO taskParamBO) {
        try {
            ContractTask contractTask = taskParamBO.getContractTask();
            ContractSubTaskDO lklOrgSubTask = getLklV3TypeInContractSubTaskDO(contractTask);
            LklOpenUnionPayTradeParamBO lklOpenUnionPayTradeParamBO = getLklOpenUnionPayTradeParamBO(taskParamBO.getContractTask().getMerchant_sn(), lklOrgSubTask);
            if (Objects.nonNull(lklOrgSubTask)) {
                ContractSubTaskDO unionPayTask = getUnionPayContractSubTaskDO(lklOrgSubTask.getPTaskId());
                if (Objects.nonNull(unionPayTask) && unionPayTask.getStatusInfluPTask() == 1) {
                    ContractResponse contractResponse = lklV3Provider.queryMerchantContractResult(lklOpenUnionPayTradeParamBO.getProviderMerchantId());
                    if (contractResponse.isSuccess()) {
                        String status = WosaiMapUtils.getString(contractResponse.getTradeParam(), "status");
                        if (!MerchantProviderParamsExtDO.UNION_PAY_SUCCESS.equals(status)) {
                            throw new ContractBizException("云闪付开通失败");
                        }
                    }
                }
            }
            MerchantProviderParamsDO merchantProviderParamsDO = getAndSaveUnionPayProviderParams(taskParamBO, lklOpenUnionPayTradeParamBO);
            createTerminalBindTask(merchantProviderParamsDO);
            ContractResponse contractResponse = new ContractResponse();
            contractResponse.setMerchantProviderParamsId(merchantProviderParamsDO.getId());
            contractResponse.setCode(ContractSubTaskHandler.RESULT_CODE_SUCCESS);
            contractResponse.setRequestParam(Objects.nonNull(lklOrgSubTask) ? JSON.parseObject(lklOrgSubTask.getRequestBody()) : null);
            contractResponse.setTradeParam(JSON.parseObject(JSON.toJSONString(lklOpenUnionPayTradeParamBO)));
            contractResponse.setResponseParam(Objects.nonNull(lklOrgSubTask) ? JSON.parseObject(lklOrgSubTask.getResponseBody()) : null);
            contractResponse.setMessage("success");
            return contractResponse;
        } catch (ContractSysException | ContractBizException e) {
            return new ContractResponse(e.getCode(), e.getMessage(), null, null, null, null);
        } catch (Exception e) {
            return new ContractResponse(ContractSubTaskHandler.RESULT_CODE_SYSTEM_EXCEPTION, e.getMessage(), null, null, null, null);
        }
    }



    /**
     * 基于拉卡拉云闪付的京东钱包
     *
     * @param taskParamBO 任务参数
     * @return 任务处理结果
     */
    private ContractResponse processJdInsertTask(TaskParamBO taskParamBO) {
        try {
            ContractTask contractTask = taskParamBO.getContractTask();
            ContractSubTaskDO lklOrgSubTask = getLklV3TypeInContractSubTaskDO(contractTask);
            LklOpenUnionPayTradeParamBO lklOpenUnionPayTradeParamBO = getLklOpenUnionPayTradeParamBO(taskParamBO.getContractTask().getMerchant_sn(), lklOrgSubTask);
            MerchantProviderParamsDO merchantProviderParamsDO = getAndSaveJdPayProviderParams(taskParamBO, lklOpenUnionPayTradeParamBO);
            ContractResponse contractResponse = new ContractResponse();
            contractResponse.setMerchantProviderParamsId(merchantProviderParamsDO.getId());
            contractResponse.setCode(ContractSubTaskHandler.RESULT_CODE_SUCCESS);
            contractResponse.setRequestParam(Objects.nonNull(lklOrgSubTask) ? JSON.parseObject(lklOrgSubTask.getRequestBody()) : null);
            contractResponse.setTradeParam(JSON.parseObject(JSON.toJSONString(lklOpenUnionPayTradeParamBO)));
            contractResponse.setResponseParam(Objects.nonNull(lklOrgSubTask) ? JSON.parseObject(lklOrgSubTask.getResponseBody()) : null);
            contractResponse.setMessage("success");
            return contractResponse;
        } catch (ContractSysException | ContractBizException e) {
            return new ContractResponse(e.getCode(), e.getMessage(), null, null, null, null);
        } catch (Exception e) {
            return new ContractResponse(ContractSubTaskHandler.RESULT_CODE_SYSTEM_EXCEPTION, e.getMessage(), null, null, null, null);
        }
    }



    /**
     * 创建商户级别终端绑定任务
     *
     * @param merchantProviderParamsDO 商户交易配置参数
     */
    private void createTerminalBindTask(MerchantProviderParamsDO merchantProviderParamsDO) {
        if (Objects.isNull(merchantProviderParamsDO)) {
            return;
        }
        providerTerminalTaskRepository.addBoundTerminalTask(merchantProviderParamsDO.getMerchantSn(),
                merchantProviderParamsDO.getPayMerchantId(),
                merchantProviderParamsDO.getProvider(),
                merchantProviderParamsDO.getPayway(),
                ProviderTerminalTaskTypeEnum.BOUND_ALL_TERMINAL.getType(),
                null, null, null);
    }


    private LklOpenUnionPayTradeParamBO getLklOpenUnionPayTradeParamBO(String merchantSn, ContractSubTaskDO lklOrgSubTask) {
        if (Objects.isNull(lklOrgSubTask)) {
            return getLklOpenUnionPayTradeParamByMerchantConfig(merchantSn);
        }
        Map<String, Object> lklV3ContractRspMap = JSON.parseObject(lklOrgSubTask.getResponseBody());
        if (MapUtils.isEmpty(lklV3ContractRspMap)) {
            throw new ContractBizException("拉卡拉机构进件子任务返回结果为空  subTask id = " + lklOrgSubTask.getId());
        }
        Optional<Map> callbackMsgOpt = ConvertUtil.castToExpectedList(BeanUtil.getNestedProperty(lklV3ContractRspMap, "callback_msg"), Map.class).stream().findFirst();
        if (!callbackMsgOpt.isPresent()) {
            throw new ContractBizException("拉卡拉机构进件子任务返回结果callback_msg为空, subTask id = " + lklOrgSubTask.getId());
        }
        return getLklOpenUnionPayTradeParamBOByCallBackMsg(callbackMsgOpt.get());
    }

    /**
     * 注:拉卡拉的返回报文格式不定
     */
    public LklOpenUnionPayTradeParamBO getLklOpenUnionPayTradeParamBOByCallBackMsg(Map<?, ?> callBackMsgMap) {
        LklOpenUnionPayTradeParamBO lklOpenUnionPayTradeParamBO = new LklOpenUnionPayTradeParamBO();
        ObjectExtensionUtils.ifNonNull(BeanUtil.getNestedProperty(callBackMsgMap, "data.merCupNo"),
                t -> ConvertUtil.castToExpectedType(t, String.class).ifPresent(lklOpenUnionPayTradeParamBO::setProviderMerchantId));
        if (StringUtils.isBlank(lklOpenUnionPayTradeParamBO.getProviderMerchantId())) {
            ObjectExtensionUtils.ifNonNull(BeanUtil.getNestedProperty(callBackMsgMap, "respData.merCupNo"),
                    t -> ConvertUtil.castToExpectedType(t, String.class).ifPresent(lklOpenUnionPayTradeParamBO::setProviderMerchantId));
        }
        ObjectExtensionUtils.ifNonNull(BeanUtil.getNestedProperty(callBackMsgMap, "data.termDatas"),
                termData -> ConvertUtil.castToExpectedList(termData, Map.class)
                        .stream().findFirst().ifPresent(map ->
                                ObjectExtensionUtils.ifNonNull(BeanUtil.getNestedProperty(map, "termNo"),
                                        termId -> ConvertUtil.castToExpectedType(termId, String.class).ifPresent(lklOpenUnionPayTradeParamBO::setTermId))
                        ));
        if (Objects.isNull(lklOpenUnionPayTradeParamBO.getTermId())) {
            ObjectExtensionUtils.ifNonNull(BeanUtil.getNestedProperty(callBackMsgMap, "respData.termDatas"),
                    termData -> ConvertUtil.castToExpectedList(termData, Map.class)
                            .stream().findFirst().ifPresent(map ->
                                    ObjectExtensionUtils.ifNonNull(BeanUtil.getNestedProperty(map, "termNo"),
                                            termId -> ConvertUtil.castToExpectedType(termId, String.class).ifPresent(lklOpenUnionPayTradeParamBO::setTermId))
                            ));
        }
        if (StringUtils.isAnyBlank(lklOpenUnionPayTradeParamBO.getProviderMerchantId(), lklOpenUnionPayTradeParamBO.getTermId())) {
            throw new ContractBizException("银联商户号或者终端号为空");
        }
        return lklOpenUnionPayTradeParamBO;
    }

    private LklOpenUnionPayTradeParamBO getLklOpenUnionPayTradeParamByMerchantConfig(String merchantSn) {
        Map<?, ?> merchant = merchantService.getMerchantBySn(merchantSn);
        if (WosaiMapUtils.isEmpty(merchant)) {
            throw new ContractBizException("商户不存在, merchantSn:" + merchantSn);
        }
        String merchantId = WosaiMapUtils.getString(merchant, DaoConstants.ID);
        LklOpenUnionPayTradeParamBO lklOpenUnionPayTradeParamBO = new LklOpenUnionPayTradeParamBO();
        Map<String, Object> merchantConfig = tradeConfigService.getMerchantConfigByMerchantIdAndPayway(merchantId, null);
        if (Objects.isNull(merchantConfig)) {
            throw new ContractBizException("商户配置不存在, merchantId:" + merchantId);
        }
        ObjectExtensionUtils.ifNonNull(BeanUtil.getNestedProperty(merchantConfig, String.format("%s.%s.%s",
                        MerchantConfig.PARAMS, TransactionParam.LAKALA_TRADE_PARAMS, TransactionParam.LAKALA_TERM_ID)),
                t -> lklOpenUnionPayTradeParamBO.setTermId(ConvertUtil.castToExpectedType(t, String.class).orElse(null)));
        ObjectExtensionUtils.ifNonNull(BeanUtil.getNestedProperty(merchantConfig, String.format("%s.%s.%s",
                        MerchantConfig.PARAMS, TransactionParam.LAKALA_TRADE_PARAMS, TransactionParam.LAKALA_MERC_ID)),
                t -> lklOpenUnionPayTradeParamBO.setProviderMerchantId(ConvertUtil.castToExpectedType(t, String.class).orElse(null)));
        if (StringUtils.isAnyBlank(lklOpenUnionPayTradeParamBO.getProviderMerchantId(), lklOpenUnionPayTradeParamBO.getTermId())) {
            throw new ContractBizException("银联商户号或者终端号为空, merchantSn = " + merchantSn);
        }
        return lklOpenUnionPayTradeParamBO;
    }

    /**
     * 获取lklV3进件子任务
     * 注意:获取的子任务可能是当前主任务的子任务,也可能是以往进件子任务
     *
     * @param contractTask 当前进件主任务
     * @return lklV3进件子任务(type = 5)
     */
    public ContractSubTaskDO getLklV3TypeInContractSubTaskDO(ContractTask contractTask) {
        return contractSubTaskDAO.listContractSubTaskDOs(contractTask.getMerchant_sn(), ProviderUtil.LKL_V3_PROVIDER_CHANNEL,
                        ContractSubTaskTypeEnum.MERCHANT_OPENING.getValue(), ContractSubTaskProcessStatusEnum.PROCESS_SUCCESS.getValue())
                .stream()
                .filter(contractSubTaskDO ->
                        (Objects.isNull(contractSubTaskDO.getPayway()) || Objects.equals(contractSubTaskDO.getPayway(), PaywayEnum.ACQUIRER.getValue())))
                .max(Comparator.comparing(ContractSubTaskDO::getCreateAt)).orElse(null);
    }

    public ContractSubTaskDO getUnionPayContractSubTaskDO(Long pTaskId) {
        if (Objects.isNull(pTaskId)) {
            return null;
        }
        return contractSubTaskDAO.listByPTaskId(pTaskId).stream()
                .filter(r -> PaywayEnum.UNIONPAY.getValue().equals(r.getPayway()))
                .findFirst().orElse(null);
    }


    private MerchantProviderParamsDO getAndSaveUnionPayProviderParams(TaskParamBO taskParamBO, LklOpenUnionPayTradeParamBO lklOpenUnionPayTradeParamBO) {
        ContractTask contractTask = taskParamBO.getContractTask();
        ContractChannel contractChannel = taskParamBO.getContractChannel();
        MerchantProviderParamsDO merchantProviderParamsDO = getMerchantProviderParamsDO(lklOpenUnionPayTradeParamBO, contractTask, contractChannel);
        merchantProviderParamsDO.setPayway(PaywayEnum.UNIONPAY.getValue());
        merchantProviderParamsDO.setPayMerchantId(lklOpenUnionPayTradeParamBO.getProviderMerchantId());
        Optional<MerchantProviderParamsDO> merchantProviderParamsOptional = merchantProviderParamsDAO
                .getMerChantProviderParams(merchantProviderParamsDO.getMerchantSn(), merchantProviderParamsDO.getRuleGroupId(),
                        merchantProviderParamsDO.getContractRule(), merchantProviderParamsDO.getChannelNo());
        if (merchantProviderParamsOptional.isPresent()) {
            merchantProviderParamsDO.setId(merchantProviderParamsOptional.get().getId());
            merchantProviderParamsDO.setCtime(null);
            merchantProviderParamsDAO.updateByPrimaryKey(merchantProviderParamsDO);
            return merchantProviderParamsDO;
        }
        Integer effectRows = merchantProviderParamsDAO.saveMerchantParameters(merchantProviderParamsDO);
        if (Objects.equals(effectRows, 0)) {
            throw new ContractSysException("数据插入异常,新增交易参数失败, merchantSn = " + contractTask.getMerchant_sn());
        }
        return merchantProviderParamsDO;
    }



    private MerchantProviderParamsDO getAndSaveJdPayProviderParams(TaskParamBO taskParamBO, LklOpenUnionPayTradeParamBO lklOpenUnionPayTradeParamBO) {
        ContractTask contractTask = taskParamBO.getContractTask();
        ContractChannel contractChannel = taskParamBO.getContractChannel();
        MerchantProviderParamsDO merchantProviderParamsDO = getMerchantProviderParamsDO(lklOpenUnionPayTradeParamBO, contractTask, contractChannel);
        merchantProviderParamsDO.setPayway(PaywayEnum.JD_WALLET.getValue());
        /**注意这里填写null,是因为代码中很多地方都是用payMerchantId获取对应的交易参数,如果这里填写云闪付的商户号,
         * 就有可能导致在
         * @see MerchantProviderParamsMapper#getByPayMerchantId(java.lang.String)
         *导致取值错误
         */
        merchantProviderParamsDO.setPayMerchantId(null);
        Optional<MerchantProviderParamsDO> merchantProviderParamsOptional = merchantProviderParamsDAO
                .getMerChantProviderParams(merchantProviderParamsDO.getMerchantSn(), merchantProviderParamsDO.getRuleGroupId(),
                        merchantProviderParamsDO.getContractRule(), merchantProviderParamsDO.getChannelNo());
        if (merchantProviderParamsOptional.isPresent()) {
            merchantProviderParamsDO.setId(merchantProviderParamsOptional.get().getId());
            merchantProviderParamsDO.setCtime(null);
            merchantProviderParamsDAO.updateByPrimaryKey(merchantProviderParamsDO);
            return merchantProviderParamsDO;
        }
        Integer effectRows = merchantProviderParamsDAO.saveMerchantParameters(merchantProviderParamsDO);
        if (Objects.equals(effectRows, 0)) {
            throw new ContractSysException("数据插入异常,新增交易参数失败, merchantSn = " + contractTask.getMerchant_sn());
        }
        return merchantProviderParamsDO;
    }

    /**
     * 查询终端报备结果
     *
     * @param merCupNo 银联商户号
     * @param termNo   终端号
     * @return 终端报备结果
     */
    @Override
    public ContractResponse queryTermContractResult(String merCupNo, String termNo) {
        return lklV3Provider.queryTermContractResult(merCupNo, termNo);
    }

    /**
     * 获取商户入网时生成的终端号
     *
     * @param merchantSn 商户号
     * @return 终端号
     */
    @Override
    public String getMerchantTermNo(String merchantSn) {
        return lklV3Provider.getMerchantTermNo(merchantSn);
    }


    /**
     * 解绑操作,直接返回成功
     *
     * @param logOutTermInfoDTO 请求dto
     * @param payWay            支付源
     * @param terminalSn        终端号
     * @return 解绑结果
     */
    @Override
    public ContractResponse unbindTerminal(LogOutTermInfoDTO logOutTermInfoDTO, int payWay, String terminalSn) {
        ContractResponse contractResponse = new ContractResponse();
        contractResponse.setCode(CommonResult.SUCCESS);
        contractResponse.setMessage("unbind success");
        contractResponse.setResponseParam(Maps.newHashMap());
        contractResponse.setResponseParam(Maps.newHashMap());
        return contractResponse;
    }



    /**
     * 构建MerchantProviderParamsDO 只有payway和pay_merchant_id没有赋值,需要调用方单独赋值
     * @param lklOpenUnionPayTradeParamBO
     * @param contractTask
     * @param contractChannel
     * @return
     */
    @NotNull
    private MerchantProviderParamsDO getMerchantProviderParamsDO(LklOpenUnionPayTradeParamBO lklOpenUnionPayTradeParamBO, ContractTask contractTask, ContractChannel contractChannel) {
        MerchantProviderParamsDO merchantProviderParamsDO = new MerchantProviderParamsDO();
        merchantProviderParamsDO.setId(UUID.randomUUID().toString());
        merchantProviderParamsDO.setCtime(System.currentTimeMillis());
        merchantProviderParamsDO.setMtime(System.currentTimeMillis());
        merchantProviderParamsDO.setDeleted(DeleteStatusEnum.NO_DELETED.getValue());
        merchantProviderParamsDO.setMerchantSn(contractTask.getMerchant_sn());
        merchantProviderParamsDO.setOutMerchantSn(contractTask.getMerchant_sn());
        merchantProviderParamsDO.setChannelNo(contractChannel.getChannel_no());
        merchantProviderParamsDO.setParentMerchantId(contractChannel.getChannel_no());
        merchantProviderParamsDO.setContractRule(contractChannel.getChannel());
        merchantProviderParamsDO.setRuleGroupId(contractTask.getRule_group_id());
        merchantProviderParamsDO.setProvider(ProviderEnum.PROVIDER_LKL_OPEN.getValue());
        merchantProviderParamsDO.setParentMerchantId(merchantProviderParamsDO.getProviderMerchantId());
        merchantProviderParamsDO.setProviderMerchantId(lklOpenUnionPayTradeParamBO.getProviderMerchantId());
        merchantProviderParamsDO.setParamsConfigStatus(ConfigStatusEnum.NOT_REQUIRE_CONFIG.getValue());
        merchantProviderParamsDO.setExtra(JSON.toJSONString(lklOpenUnionPayTradeParamBO));
        return merchantProviderParamsDO;
    }


}
