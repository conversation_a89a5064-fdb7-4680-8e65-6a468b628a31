# 营业执照审核任务依赖关系优化

## 优化目标

优化 `doInsertTaskByLicenseAuditApply` 方法中的任务依赖关系，实现更合理的任务执行顺序：

1. **账户验证任务** → **主要进件任务** → **其他进件任务** → **实名授权任务** → **参数切换任务**

## 原始实现问题

### 当前逻辑
```java
// 原始代码中所有其他任务都依赖账户验证任务
if (Objects.nonNull(accountVerifySubTask)) {
    otherSubTasks.forEach(internalScheduleSubTaskDO -> {
        internalScheduleSubTaskDO.setMainTaskId(mainTask.getId());
        internalScheduleSubTaskDO.setDependOnSubTaskId(accountVerifySubTask.getId());
    });
}
```

### 问题分析
1. **并发性差**：所有任务都等待账户验证完成，无法并行执行
2. **逻辑不清晰**：实名授权任务应该依赖进件任务，而不是账户验证任务
3. **效率低下**：参数切换任务应该最后执行，但当前逻辑中可能过早执行

## 优化后的实现

### 新的依赖链设计

```
账户验证任务 (如果需要)
    ↓
主要进件任务 (当前使用的收单机构)
    ↓
其他进件任务 (并行执行)
    ↓
实名授权任务 (等待所有进件任务完成)
    ↓
参数切换任务 (最后执行)
```

### 核心优化方法

#### 1. buildOptimizedTaskDependencies
```java
private void buildOptimizedTaskDependencies(InternalScheduleMainTaskDO mainTask, 
                                           BusinessLicenseAuditApplyDTO businessLicenseAuditApplyDTO, 
                                           boolean isMicroUpgrade) {
    // 1. 构建账户验证任务
    // 2. 构建进件任务并区分主要/其他任务
    // 3. 构建实名授权任务
    // 4. 构建参数切换任务
    // 5. 设置依赖关系
    // 6. 插入任务
}
```

#### 2. setupTaskDependencies
```java
private void setupTaskDependencies(InternalScheduleSubTaskDO accountVerifySubTask,
                                  InternalScheduleSubTaskDO primaryReContractTask,
                                  List<InternalScheduleSubTaskDO> otherReContractTasks,
                                  InternalScheduleSubTaskDO payWayAuthTask,
                                  InternalScheduleSubTaskDO changeParamsTask,
                                  InternalScheduleMainTaskDO mainTask) {
    // 设置主任务ID
    // 设置依赖关系：账户验证 → 主要进件 → 其他进件 → 实名授权 → 参数切换
}
```

#### 3. insertTasksWithDependencies
```java
private void insertTasksWithDependencies(InternalScheduleMainTaskDO mainTask,
                                       InternalScheduleSubTaskDO accountVerifySubTask,
                                       InternalScheduleSubTaskDO primaryReContractTask,
                                       List<InternalScheduleSubTaskDO> otherReContractTasks,
                                       InternalScheduleSubTaskDO payWayAuthTask,
                                       InternalScheduleSubTaskDO changeParamsTask) {
    // 按执行顺序添加任务
    // 设置初始调度状态
    // 插入任务
}
```

## 优化效果

### 1. 提高并发性
- **原来**：所有任务串行等待账户验证
- **现在**：其他进件任务可以在主要进件任务完成后并行执行

### 2. 逻辑更清晰
- **账户验证** → 确保银行账户信息正确
- **主要进件任务** → 处理当前使用的收单机构
- **其他进件任务** → 处理其他收单机构（并行）
- **实名授权任务** → 等待所有进件完成后进行授权
- **参数切换任务** → 最后切换参数

### 3. 更好的错误处理
- 如果主要进件任务失败，其他任务会被标记为失败
- 如果账户验证失败，整个流程停止
- 依赖关系清晰，便于问题排查

### 4. 兼容性保证
- 保持原有的 `AffectPrimaryTaskStatusEnum` 逻辑
- 保持原有的任务类型和上下文结构
- 向后兼容现有的任务处理器

## 关键改进点

### 1. 主要进件任务识别
```java
// 通过 oldInUseAcquirer 确定主要进件任务
reContractSubTask.setAffectMainTaskStatus(
    Objects.equals(businessLicenseCertificationV2MainTaskContext.getOldInUseAcquirer(), reContractAcquirer)
        ? AffectPrimaryTaskStatusEnum.YES.getValue()
        : AffectPrimaryTaskStatusEnum.NO.getValue()
);
```

### 2. 任务调度状态管理
```java
// 根据依赖关系设置初始调度状态
if (accountVerifySubTask != null) {
    accountVerifySubTask.setEnableScheduledStatus(ScheduleStatusEnum.CAN.getValue());
    // 其他任务设置为不可调度，等待依赖任务完成
}
```

### 3. 依赖关系设置
```java
// 设置清晰的依赖链
if (accountVerifySubTask != null && primaryReContractTask != null) {
    primaryReContractTask.setDependOnSubTaskId(accountVerifySubTask.getId());
}
if (primaryReContractTask != null) {
    otherReContractTasks.forEach(task -> task.setDependOnSubTaskId(primaryReContractTask.getId()));
    payWayAuthTask.setDependOnSubTaskId(primaryReContractTask.getId());
}
changeParamsTask.setDependOnSubTaskId(payWayAuthTask.getId());
```

## 测试建议

1. **单元测试**：测试各种场景下的依赖关系设置
2. **集成测试**：验证任务执行顺序的正确性
3. **性能测试**：对比优化前后的执行效率
4. **异常测试**：验证错误场景下的任务状态处理

## 注意事项

1. **数据库事务**：确保任务插入的原子性
2. **并发控制**：避免任务状态更新的竞态条件
3. **监控告警**：添加任务执行状态的监控
4. **回滚机制**：保留原有方法作为备用方案
