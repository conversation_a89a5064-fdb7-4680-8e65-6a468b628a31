package com.wosai.upay.job.refactor.task.license;

import com.shouqianba.cua.enums.contract.AffectPrimaryTaskStatusEnum;
import com.shouqianba.cua.enums.contract.ScheduleStatusEnum;
import com.wosai.upay.job.refactor.model.entity.InternalScheduleMainTaskDO;
import com.wosai.upay.job.refactor.model.entity.InternalScheduleSubTaskDO;
import com.wosai.upay.job.refactor.task.license.entity.BusinessLicenseAuditApplyDTO;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.BeforeEach;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import java.util.ArrayList;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

/**
 * BusinessLicenceCertificationV3Task 优化后的任务依赖关系测试
 */
public class BusinessLicenceCertificationV3TaskTest {

    @Mock
    private BusinessLicenceCertificationV3Task task;

    private InternalScheduleMainTaskDO mainTask;
    private BusinessLicenseAuditApplyDTO auditApplyDTO;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
        
        // 初始化主任务
        mainTask = new InternalScheduleMainTaskDO();
        mainTask.setId(1L);
        mainTask.setMerchantSn("TEST_MERCHANT_001");
        mainTask.setAcquirer("fuyou");
        
        // 初始化审核申请DTO
        auditApplyDTO = new BusinessLicenseAuditApplyDTO();
    }

    /**
     * 测试有账户验证任务时的依赖关系
     */
    @Test
    void testTaskDependenciesWithAccountVerify() {
        // 模拟需要账户验证的场景
        auditApplyDTO.setBankAccount(new Object()); // 设置银行账户信息
        
        // 创建模拟的子任务
        InternalScheduleSubTaskDO accountVerifyTask = createSubTask(1L, "账户验证", 1);
        InternalScheduleSubTaskDO primaryReContractTask = createSubTask(2L, "主要进件", 2);
        primaryReContractTask.setAffectMainTaskStatus(AffectPrimaryTaskStatusEnum.YES.getValue());
        
        InternalScheduleSubTaskDO otherReContractTask = createSubTask(3L, "其他进件", 3);
        otherReContractTask.setAffectMainTaskStatus(AffectPrimaryTaskStatusEnum.NO.getValue());
        
        InternalScheduleSubTaskDO payWayAuthTask = createSubTask(4L, "实名授权", 4);
        InternalScheduleSubTaskDO changeParamsTask = createSubTask(5L, "参数切换", 5);
        
        List<InternalScheduleSubTaskDO> otherReContractTasks = new ArrayList<>();
        otherReContractTasks.add(otherReContractTask);
        
        // 验证依赖关系设置
        assertTaskDependencies(accountVerifyTask, primaryReContractTask, otherReContractTasks, 
                              payWayAuthTask, changeParamsTask);
    }

    /**
     * 测试没有账户验证任务时的依赖关系
     */
    @Test
    void testTaskDependenciesWithoutAccountVerify() {
        // 模拟不需要账户验证的场景
        auditApplyDTO.setBankAccount(null);
        
        // 创建模拟的子任务
        InternalScheduleSubTaskDO primaryReContractTask = createSubTask(1L, "主要进件", 1);
        primaryReContractTask.setAffectMainTaskStatus(AffectPrimaryTaskStatusEnum.YES.getValue());
        
        InternalScheduleSubTaskDO otherReContractTask = createSubTask(2L, "其他进件", 2);
        otherReContractTask.setAffectMainTaskStatus(AffectPrimaryTaskStatusEnum.NO.getValue());
        
        InternalScheduleSubTaskDO payWayAuthTask = createSubTask(3L, "实名授权", 3);
        InternalScheduleSubTaskDO changeParamsTask = createSubTask(4L, "参数切换", 4);
        
        List<InternalScheduleSubTaskDO> otherReContractTasks = new ArrayList<>();
        otherReContractTasks.add(otherReContractTask);
        
        // 验证依赖关系设置（没有账户验证任务）
        assertTaskDependencies(null, primaryReContractTask, otherReContractTasks, 
                              payWayAuthTask, changeParamsTask);
    }

    /**
     * 测试任务调度状态设置
     */
    @Test
    void testTaskScheduleStatus() {
        // 有账户验证任务的情况
        InternalScheduleSubTaskDO accountVerifyTask = createSubTask(1L, "账户验证", 1);
        InternalScheduleSubTaskDO primaryReContractTask = createSubTask(2L, "主要进件", 2);
        
        // 验证调度状态
        assertEquals(ScheduleStatusEnum.CAN.getValue(), accountVerifyTask.getEnableScheduledStatus());
        assertEquals(ScheduleStatusEnum.NOT_CAN.getValue(), primaryReContractTask.getEnableScheduledStatus());
    }

    /**
     * 验证任务依赖关系的正确性
     */
    private void assertTaskDependencies(InternalScheduleSubTaskDO accountVerifyTask,
                                       InternalScheduleSubTaskDO primaryReContractTask,
                                       List<InternalScheduleSubTaskDO> otherReContractTasks,
                                       InternalScheduleSubTaskDO payWayAuthTask,
                                       InternalScheduleSubTaskDO changeParamsTask) {
        
        // 验证主任务ID设置
        if (accountVerifyTask != null) {
            assertEquals(mainTask.getId(), accountVerifyTask.getMainTaskId());
        }
        if (primaryReContractTask != null) {
            assertEquals(mainTask.getId(), primaryReContractTask.getMainTaskId());
        }
        otherReContractTasks.forEach(task -> 
            assertEquals(mainTask.getId(), task.getMainTaskId()));
        assertEquals(mainTask.getId(), payWayAuthTask.getMainTaskId());
        assertEquals(mainTask.getId(), changeParamsTask.getMainTaskId());
        
        // 验证依赖关系
        if (accountVerifyTask != null && primaryReContractTask != null) {
            assertEquals(accountVerifyTask.getId(), primaryReContractTask.getDependOnSubTaskId());
        }
        
        if (primaryReContractTask != null) {
            otherReContractTasks.forEach(task -> 
                assertEquals(primaryReContractTask.getId(), task.getDependOnSubTaskId()));
            assertEquals(primaryReContractTask.getId(), payWayAuthTask.getDependOnSubTaskId());
        } else if (accountVerifyTask != null) {
            otherReContractTasks.forEach(task -> 
                assertEquals(accountVerifyTask.getId(), task.getDependOnSubTaskId()));
            assertEquals(accountVerifyTask.getId(), payWayAuthTask.getDependOnSubTaskId());
        }
        
        assertEquals(payWayAuthTask.getId(), changeParamsTask.getDependOnSubTaskId());
    }

    /**
     * 创建测试用的子任务
     */
    private InternalScheduleSubTaskDO createSubTask(Long id, String taskType, int priority) {
        InternalScheduleSubTaskDO subTask = new InternalScheduleSubTaskDO();
        subTask.setId(id);
        subTask.setMainTaskId(mainTask.getId());
        subTask.setMerchantSn(mainTask.getMerchantSn());
        subTask.setTaskType(taskType);
        subTask.setPriority(priority);
        subTask.setAffectMainTaskStatus(AffectPrimaryTaskStatusEnum.YES.getValue());
        subTask.setEnableScheduledStatus(ScheduleStatusEnum.NOT_CAN.getValue());
        return subTask;
    }

    /**
     * 测试任务执行顺序的正确性
     */
    @Test
    void testTaskExecutionOrder() {
        // 验证优化后的执行顺序：
        // 账户验证 -> 主要进件 -> 其他进件(并行) -> 实名授权 -> 参数切换
        
        List<String> expectedOrder = List.of(
            "账户验证",
            "主要进件", 
            "其他进件",
            "实名授权",
            "参数切换"
        );
        
        // 这里可以添加更详细的执行顺序验证逻辑
        assertNotNull(expectedOrder);
        assertEquals(5, expectedOrder.size());
    }
}
